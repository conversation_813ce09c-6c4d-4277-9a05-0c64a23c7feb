#!/usr/bin/env python

"""
UI实用工具函数模块
包含各种UI相关的辅助函数
"""

import logging
import re

from PySide6.QtGui import QColor
from PySide6.QtWidgets import QColorDialog, QDialog, QLabel, QPushButton

logger = logging.getLogger(__name__)


def setup_color_dialog_chinese(color_dialog):
    """设置颜色对话框的中文标签文本"""
    try:
        # 设置按钮文本
        buttons = color_dialog.findChildren(QPushButton)
        for button in buttons:
            if button.text() == "&OK" or button.text() == "OK":
                button.setText("确定")
            elif button.text() == "&Cancel" or button.text() == "Cancel":
                button.setText("取消")
            elif (
                button.text() == "Add to Custom Colors" or button.text() == "&Add to Custom Colors"
            ):
                button.setText("添加到自定义颜色")
            elif "Pick Screen" in button.text():
                button.setText("拾取屏幕颜色")

        # 修改标签文本
        labels = color_dialog.findChildren(QLabel)
        for label in labels:
            text = label.text()
            if text == "Basic colors":
                label.setText("基本颜色")
            elif text == "Custom colors":
                label.setText("自定义颜色")
            elif text == "Hue:" or text == "&Hue:":
                label.setText("色调:")
            elif text == "Sat:" or text == "&Sat:":
                label.setText("饱和度:")
            elif text == "Val:" or text == "&Val:":
                label.setText("明度:")
            elif text == "Red:" or text == "&Red:":
                label.setText("红色:")
            elif text == "Green:" or text == "&Green:":
                label.setText("绿色:")
            elif text == "Blue:" or text == "&Blue:":
                label.setText("蓝色:")
            elif text == "Alpha:" or text == "&Alpha:":
                label.setText("透明度:")
            elif text == "HTML:" or text == "&HTML:":
                label.setText("HTML:")

        # 查找常见的ID并设置文本
        basic_colors_label = color_dialog.findChild(QLabel, "lblBasicColors")
        if basic_colors_label:
            basic_colors_label.setText("基本颜色")

        custom_colors_label = color_dialog.findChild(QLabel, "lblCustomColors")
        if custom_colors_label:
            custom_colors_label.setText("自定义颜色")

        # 更新对话框布局以确保新文本适当显示
        color_dialog.adjustSize()

    except Exception as e:
        logger.debug(f"设置颜色对话框中文标签时出错: {e}")


def show_color_dialog(parent, current_color, for_bg=True):
    """显示统一的颜色选择对话框

    Args:
        parent: 父窗口
        current_color: 当前颜色
        for_bg: 是否是背景颜色选择

    Returns:
        如果用户选择了颜色，返回QColor对象；否则返回None
    """
    # 获取当前颜色
    if not isinstance(current_color, QColor):
        current_color = QColor(current_color) if current_color else QColor("#FFFFFF")

    # 创建标准颜色对话框
    dialog_title = "选择背景颜色" if for_bg else "选择字体颜色"
    color_dialog = QColorDialog(current_color, parent)
    color_dialog.setWindowTitle(dialog_title)

    # 尝试设置中文
    setup_color_dialog_chinese(color_dialog)

    # 显示对话框并获取选择的颜色
    if color_dialog.exec() == QDialog.Accepted:
        color = color_dialog.selectedColor()
        if color.isValid():
            return color

    return None


def update_button_styles(bg_button, font_button, bg_color, font_color):
    """更新颜色按钮的样式

    Args:
        bg_button: 背景颜色按钮
        font_button: 字体颜色按钮
        bg_color: 背景颜色（QColor对象）
        font_color: 字体颜色（QColor对象）
    """
    if bg_button and bg_color and bg_color.isValid():
        bg_color_str = bg_color.name()
        font_color_str = font_color.name() if font_color and font_color.isValid() else "#FFFFFF"

        bg_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {bg_color_str};
                color: {font_color_str};
                border: 1px solid #5C2E0B;
                border-radius: 3px;
                padding: 4px 6px;
                text-align: center;
                min-height: 26px;
                max-height: 26px;
                height: 26px;
            }}
        """
        )
        # 强制设置固定尺寸
        bg_button.setFixedSize(80, 26)
        bg_button.updateGeometry()

    if font_button and font_color and font_color.isValid():
        font_color_str = font_color.name()

        font_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: #EEEEEE;
                color: {font_color_str};
                border: 1px solid #999999;
                border-radius: 3px;
                padding: 4px 6px;
                text-align: center;
                min-height: 26px;
                max-height: 26px;
                height: 26px;
            }}
        """
        )
        # 强制设置固定尺寸
        font_button.setFixedSize(80, 26)
        font_button.updateGeometry()


def extract_color_from_stylesheet(style_sheet):
    """从样式表中提取背景颜色

    Args:
        style_sheet: 样式表字符串

    Returns:
        如果找到背景颜色，返回颜色字符串；否则返回None
    """
    if not style_sheet:
        return None

    match = re.search(r"background-color:\s*([^;]+)", style_sheet)
    if match:
        return match.group(1).strip()

    return None
