#!/usr/bin/env python

"""
分类模型
用于管理任务分类数据
"""

import json
import logging
import os
import uuid

logger = logging.getLogger(__name__)


class CategoryModel:
    """分类模型类，用于管理任务分类"""

    def __init__(self):
        """初始化分类模型"""
        # 分类数据文件路径
        self.categories_file = os.path.join("data", "categories.json")

        # 分类数据
        self.categories = []

        # 加载分类数据
        self.load_categories()

        logger.debug("分类模型初始化完成")

    def load_categories(self):
        """加载分类数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.categories_file), exist_ok=True)

            # 如果文件不存在，创建默认分类
            if not os.path.exists(self.categories_file):
                self.categories = self.create_default_categories()
                self.save_categories()
            else:
                # 从文件加载分类
                with open(self.categories_file, encoding="utf-8") as f:
                    self.categories = json.load(f)

            logger.info(f"成功加载 {len(self.categories)} 个分类")
        except Exception as e:
            logger.error(f"加载分类数据失败: {e}")
            self.categories = self.create_default_categories()

    def save_categories(self):
        """保存分类数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.categories_file), exist_ok=True)

            # 保存到文件
            with open(self.categories_file, "w", encoding="utf-8") as f:
                json.dump(self.categories, f, ensure_ascii=False, indent=2)

            logger.info(f"成功保存 {len(self.categories)} 个分类")
            return True
        except Exception as e:
            logger.error(f"保存分类数据失败: {e}")
            return False

    def create_default_categories(self):
        """创建默认分类"""
        return [
            {"id": "system", "name": "系统", "type": "system", "description": "系统相关任务"},
            {"id": "backup", "name": "备份", "type": "system", "description": "备份相关任务"},
            {"id": "automation", "name": "自动化", "type": "system", "description": "自动化任务"},
            {"id": "network", "name": "网络", "type": "system", "description": "网络相关任务"},
            {"id": "media", "name": "媒体", "type": "system", "description": "媒体相关任务"},
            {"id": "custom1", "name": "工作", "type": "custom", "description": "工作相关任务"},
            {"id": "custom2", "name": "学习", "type": "custom", "description": "学习相关任务"},
            {"id": "custom3", "name": "生活", "type": "custom", "description": "生活相关任务"},
        ]

    def get_all_categories(self):
        """获取所有分类"""
        return self.categories

    def get_category_by_id(self, category_id):
        """根据ID获取分类"""
        for category in self.categories:
            if category.get("id") == category_id:
                return category
        return None

    def get_categories_by_type(self, category_type):
        """根据类型获取分类"""
        return [c for c in self.categories if c.get("type") == category_type]

    def add_category(self, name, description="", category_type="custom", icon=""):
        """添加新分类"""
        # 检查名称是否已存在
        if any(c.get("name") == name for c in self.categories):
            logger.warning(f"分类名称 '{name}' 已存在")
            return False, "分类名称已存在"

        # 创建新分类
        new_category = {
            "id": str(uuid.uuid4()),
            "name": name,
            "type": category_type,
            "description": description,
            "icon": icon,
        }

        # 添加到分类列表
        self.categories.append(new_category)

        # 保存到文件
        if self.save_categories():
            logger.info(f"成功添加分类: {name}")
            return True, new_category["id"]
        else:
            logger.error(f"添加分类失败: {name}")
            return False, "保存分类数据失败"

    def update_category(self, category_id, name=None, description=None, icon=None):
        """更新分类"""
        # 查找分类
        category = self.get_category_by_id(category_id)
        if not category:
            logger.warning(f"分类不存在: {category_id}")
            return False, "分类不存在"

        # 检查名称是否已存在（如果要更改名称）
        if name and name != category.get("name"):
            if any(c.get("name") == name and c.get("id") != category_id for c in self.categories):
                logger.warning(f"分类名称 '{name}' 已存在")
                return False, "分类名称已存在"

        # 更新分类数据
        if name is not None:
            category["name"] = name
        if description is not None:
            category["description"] = description
        if icon is not None:
            category["icon"] = icon

        # 保存到文件
        if self.save_categories():
            logger.info(f"成功更新分类: {category_id}")
            return True, None
        else:
            logger.error(f"更新分类失败: {category_id}")
            return False, "保存分类数据失败"

    def delete_category(self, category_id):
        """删除分类"""
        # 查找分类
        category = self.get_category_by_id(category_id)
        if not category:
            logger.warning(f"分类不存在: {category_id}")
            return False, "分类不存在"

        # 检查是否为系统分类
        if category.get("type") == "system":
            logger.warning(f"无法删除系统分类: {category_id}")
            return False, "系统分类不可删除"

        # 从列表中删除
        self.categories = [c for c in self.categories if c.get("id") != category_id]

        # 保存到文件
        if self.save_categories():
            logger.info(f"成功删除分类: {category_id}")
            return True, None
        else:
            logger.error(f"删除分类失败: {category_id}")
            return False, "保存分类数据失败"

    def setupModelData(self):
        """重新加载分类数据 - 用于刷新模型"""
        try:
            # 重新加载分类数据
            self.load_categories()
            logger.info("分类模型数据已重新加载")
            return True
        except Exception as e:
            logger.error(f"重新加载分类模型数据失败: {e}")
            return False
