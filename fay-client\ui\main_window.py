#!/usr/bin/env python

"""
FayAutoDesk 主窗口模块
实现应用程序的主界面
"""

import logging
import os
import sys
import uuid
from datetime import datetime

from PySide6.QtCore import QPoint, QSettings, QSize, Qt, QTimer
from PySide6.QtGui import (
    QAction,
    QBrush,
    QColor,
    QIcon,
    QKeySequence,
    QPainter,
    QPen,
    QPixmap,
    QPolygon,
)
from PySide6.QtWidgets import (
    QApplication,
    QCheckBox,
    QDialog,
    QFrame,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QMainWindow,
    QMenu,
    QMessageBox,
    QPushButton,
    QScrollArea,
    QSizePolicy,
    QSplitter,
    QStatusBar,
    QStyle,
    QSystemTrayIcon,
    QTableWidget,
    QTableWidgetItem,
    QToolBar,
    QVBoxLayout,
    QWidget,
)

from .models.task_manager import TaskManager
from .utils import GlobalEventFilter
from .utils.styles import get_application_style

# 设置日志
logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """FayAutoDesk 主窗口类"""

    def handle_task_changed(self, event_type=None, task_data=None):
        """任务变更事件处理器, 自动刷新任务列表"""
        logger.info("任务数据已变更, 刷新任务列表")
        self.load_tasks()

    def __init__(self):
        super().__init__()

        # 设置窗口基本属性
        self.setWindowTitle("FayAutoDesk - PC端自动化工具")
        self.resize(1200, 800)

        # 设置应用图标
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "resources", "icons", "app_icon.ico"
        )
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            logger.info(f"已设置应用图标: {icon_path}")
        else:
            logger.warning(f"应用图标文件不存在: {icon_path}")

        # 应用全局样式表
        self.setStyleSheet(get_application_style())

        # 初始化属性
        self.current_file_path = None
        self.has_unsaved_changes = False
        self.is_running = False
        self.start_time = datetime.now()

        # 初始化设置
        self.settings = QSettings("FayTeam", "FayAutoDesk")

        # 使用全局组件管理器获取任务管理器实例
        try:
            from .models import component_manager

            self.task_manager = component_manager.get_task_manager()
            logger.info("成功获取全局任务管理器实例")
        except Exception as e:
            logger.warning(f"无法获取全局任务管理器，创建本地实例: {e}")
            from .models.sqlite_storage import SQLiteStorage

            storage = SQLiteStorage()
            self.task_manager = TaskManager(storage=storage)

        # 任务数据（从任务管理器获取）
        self.all_tasks_data = []

        # 分类数据
        self.categories = []
        self.current_category_id = None

        # 初始化分类模型
        try:
            from src.models.category_model import CategoryModel

            self.category_model = CategoryModel()
            logger.info("成功初始化分类模型")
        except Exception as e:
            logger.warning(f"初始化分类模型失败: {e}")
            self.category_model = None

        # 创建运行时间计时器
        self.run_timer = QTimer(self)
        self.run_timer.timeout.connect(self.update_run_time)

        # 首先创建所有动作
        self._create_colored_icons()
        self._create_actions()

        # 先创建工具栏，再创建菜单栏
        self._create_tool_bar()
        self._create_menus()

        # 创建中央部件
        self._create_central_widget()

        # 创建状态栏
        self._create_status_bar()

        # 创建系统托盘
        self._create_system_tray()

        # 设置分类树
        self.setup_category_tree()

        # 创建并安装事件过滤器，用于捕获全局热键
        self.event_filter = GlobalEventFilter(self)
        QApplication.instance().installEventFilter(self.event_filter)

        # 加载用户设置
        self.load_settings()

        # 加载任务数据
        self.load_tasks()

        # 更新状态栏
        self.update_status_text()

        # 启动运行时间计时器
        self.run_timer.start(1000)  # 1秒更新一次

        # 显示欢迎信息
        self.statusBar().showMessage("系统已准备就绪", 3000)
        # 注册任务变更事件处理
        self.task_manager.register_callback("task_changed", self.handle_task_changed)

        # 初始化增强功能组件
        self.init_enhanced_features()

        logger.info("主窗口已初始化完成")

    def _create_colored_icons(self):
        """创建彩色图标"""
        # 创建彩色图标
        self.run_icon = self._create_icon("run", "#28a745")  # 绿色
        self.stop_icon = self._create_icon("stop", "#dc3545")  # 红色
        self.pause_icon = self._create_icon("pause", "#ffc107")  # 黄色
        self.up_icon = self._create_icon("up", "#007bff")  # 蓝色
        self.down_icon = self._create_icon("down", "#007bff")  # 蓝色
        self.import_icon = self._create_icon("import", "#fd7e14")  # 橙色
        self.export_icon = self._create_icon("export", "#6f42c1")  # 紫色
        self.log_icon = self._create_icon("log", "#343a40")  # 深灰色
        self.new_icon = self._create_icon("new", "#17a2b8")  # 青色
        self.edit_icon = self._create_icon("edit", "#6c757d")  # 灰色
        self.copy_icon = self._create_icon("copy", "#20c997")  # 绿松石色
        self.delete_icon = self._create_icon("delete", "#dc3545")  # 红色
        self.refresh_icon = self._create_icon("refresh", "#007bff")  # 蓝色
        self.search_icon = self._create_icon("search", "#6f42c1")  # 紫色
        self.settings_icon = self._create_icon("settings", "#6c757d")  # 灰色

    def _create_icon(self, icon_type, color, size=24):
        """创建彩色图标"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 设置画笔和画刷
        pen = QPen(QColor(color))
        pen.setWidth(2)
        painter.setPen(pen)
        painter.setBrush(QBrush(QColor(color)))

        # 根据图标类型绘制不同形状
        if icon_type == "run":
            # 绿色播放三角形
            points = QPolygon(
                [
                    QPoint(int(size * 0.3), int(size * 0.2)),
                    QPoint(int(size * 0.3), int(size * 0.8)),
                    QPoint(int(size * 0.8), int(size * 0.5)),
                ]
            )
            painter.drawPolygon(points)
        elif icon_type == "stop":
            # 红色停止方块
            painter.drawRect(int(size * 0.25), int(size * 0.25), int(size * 0.5), int(size * 0.5))
        elif icon_type == "up":
            # 蓝色向上箭头
            points = QPolygon(
                [
                    QPoint(int(size * 0.5), int(size * 0.2)),
                    QPoint(int(size * 0.3), int(size * 0.5)),
                    QPoint(int(size * 0.7), int(size * 0.5)),
                ]
            )
            painter.drawPolygon(points)
            painter.drawRect(int(size * 0.45), int(size * 0.5), int(size * 0.1), int(size * 0.3))
        elif icon_type == "down":
            # 蓝色向下箭头
            points = QPolygon(
                [
                    QPoint(int(size * 0.5), int(size * 0.8)),
                    QPoint(int(size * 0.3), int(size * 0.5)),
                    QPoint(int(size * 0.7), int(size * 0.5)),
                ]
            )
            painter.drawPolygon(points)
            painter.drawRect(int(size * 0.45), int(size * 0.2), int(size * 0.1), int(size * 0.3))
        elif icon_type == "import":
            # 橙色导入箭头
            painter.drawRect(int(size * 0.2), int(size * 0.7), int(size * 0.6), int(size * 0.1))
            points = QPolygon(
                [
                    QPoint(int(size * 0.5), int(size * 0.2)),
                    QPoint(int(size * 0.3), int(size * 0.4)),
                    QPoint(int(size * 0.7), int(size * 0.4)),
                ]
            )
            painter.drawPolygon(points)
            painter.drawRect(int(size * 0.45), int(size * 0.4), int(size * 0.1), int(size * 0.3))
        elif icon_type == "export":
            # 紫色导出箭头
            painter.drawRect(int(size * 0.2), int(size * 0.2), int(size * 0.6), int(size * 0.1))
            points = QPolygon(
                [
                    QPoint(int(size * 0.5), int(size * 0.8)),
                    QPoint(int(size * 0.3), int(size * 0.6)),
                    QPoint(int(size * 0.7), int(size * 0.6)),
                ]
            )
            painter.drawPolygon(points)
            painter.drawRect(int(size * 0.45), int(size * 0.3), int(size * 0.1), int(size * 0.3))
        elif icon_type == "log":
            # 深灰色日志文档
            painter.drawRect(int(size * 0.2), int(size * 0.15), int(size * 0.6), int(size * 0.7))
            painter.setPen(QPen(QColor("white")))
            painter.drawLine(int(size * 0.3), int(size * 0.3), int(size * 0.7), int(size * 0.3))
            painter.drawLine(int(size * 0.3), int(size * 0.45), int(size * 0.7), int(size * 0.45))
            painter.drawLine(int(size * 0.3), int(size * 0.6), int(size * 0.7), int(size * 0.6))
        elif icon_type == "pause":
            # 黄色暂停按钮
            painter.drawRect(int(size * 0.35), int(size * 0.2), int(size * 0.1), int(size * 0.6))
            painter.drawRect(int(size * 0.55), int(size * 0.2), int(size * 0.1), int(size * 0.6))
        elif icon_type == "new":
            # 青色新建文档
            painter.drawRect(int(size * 0.2), int(size * 0.15), int(size * 0.5), int(size * 0.7))
            painter.setPen(QPen(QColor("white")))
            painter.drawLine(int(size * 0.5), int(size * 0.4), int(size * 0.6), int(size * 0.4))
            painter.drawLine(int(size * 0.55), int(size * 0.35), int(size * 0.55), int(size * 0.45))
        elif icon_type == "edit":
            # 灰色编辑笔
            painter.drawRect(int(size * 0.6), int(size * 0.2), int(size * 0.15), int(size * 0.4))
            painter.drawLine(int(size * 0.2), int(size * 0.8), int(size * 0.6), int(size * 0.4))
            painter.drawRect(int(size * 0.2), int(size * 0.75), int(size * 0.1), int(size * 0.1))
        elif icon_type == "copy":
            # 绿松石色复制
            painter.drawRect(int(size * 0.25), int(size * 0.25), int(size * 0.4), int(size * 0.5))
            painter.setPen(QPen(QColor(color), 2))
            painter.setBrush(QBrush(Qt.transparent))
            painter.drawRect(int(size * 0.35), int(size * 0.35), int(size * 0.4), int(size * 0.5))
        elif icon_type == "delete":
            # 红色删除垃圾桶
            painter.drawRect(int(size * 0.3), int(size * 0.3), int(size * 0.4), int(size * 0.5))
            painter.drawRect(int(size * 0.25), int(size * 0.25), int(size * 0.5), int(size * 0.1))
            painter.drawLine(int(size * 0.4), int(size * 0.4), int(size * 0.4), int(size * 0.7))
            painter.drawLine(int(size * 0.5), int(size * 0.4), int(size * 0.5), int(size * 0.7))
            painter.drawLine(int(size * 0.6), int(size * 0.4), int(size * 0.6), int(size * 0.7))
        elif icon_type == "refresh":
            # 蓝色刷新箭头
            painter.setBrush(QBrush(Qt.transparent))
            painter.drawEllipse(int(size * 0.2), int(size * 0.2), int(size * 0.6), int(size * 0.6))
            # 箭头
            points = QPolygon(
                [
                    QPoint(int(size * 0.7), int(size * 0.3)),
                    QPoint(int(size * 0.8), int(size * 0.2)),
                    QPoint(int(size * 0.8), int(size * 0.4)),
                ]
            )
            painter.setBrush(QBrush(QColor(color)))
            painter.drawPolygon(points)
        elif icon_type == "search":
            # 紫色搜索放大镜
            painter.setBrush(QBrush(Qt.transparent))
            painter.drawEllipse(int(size * 0.2), int(size * 0.2), int(size * 0.4), int(size * 0.4))
            painter.drawLine(int(size * 0.55), int(size * 0.55), int(size * 0.75), int(size * 0.75))
        elif icon_type == "settings":
            # 灰色设置齿轮
            painter.setBrush(QBrush(Qt.transparent))
            painter.drawEllipse(int(size * 0.3), int(size * 0.3), int(size * 0.4), int(size * 0.4))
            # 齿轮齿
            for i in range(8):
                angle = i * 45
                x1 = int(size * 0.5 + size * 0.25 * 0.8 * (angle % 90 == 0 and 1 or 0.7))
                y1 = int(size * 0.5)
                painter.drawLine(int(size * 0.5), int(size * 0.5), x1, y1)

        painter.end()
        return QIcon(pixmap)

    def _create_actions(self):
        """创建所有动作"""
        # 获取标准图标
        style = self.style()

        # 文件操作动作
        new_icon = style.standardIcon(QStyle.SP_FileIcon)
        self.new_action = QAction("新建", self)
        self.new_action.setIcon(new_icon)
        self.new_action.setShortcut(QKeySequence.New)
        self.new_action.triggered.connect(self.create_new_task)

        open_icon = style.standardIcon(QStyle.SP_DialogOpenButton)
        self.open_action = QAction("打开", self)
        self.open_action.setIcon(open_icon)
        self.open_action.setShortcut(QKeySequence.Open)
        self.open_action.triggered.connect(self.open_file)

        save_icon = style.standardIcon(QStyle.SP_DialogSaveButton)
        self.save_action = QAction("保存", self)
        self.save_action.setIcon(save_icon)
        self.save_action.setShortcut(QKeySequence.Save)
        self.save_action.triggered.connect(self.save_file)

        self.save_as_action = QAction("另存为", self)
        self.save_as_action.triggered.connect(self.save_file_as)

        self.exit_action = QAction("退出", self)
        self.exit_action.setShortcut(QKeySequence.Quit)
        self.exit_action.triggered.connect(self.close)

        # 编辑操作动作
        self.cut_action = QAction("剪切", self)
        self.cut_action.setShortcut(QKeySequence.Cut)

        self.copy_action = QAction("复制", self)
        self.copy_action.setShortcut(QKeySequence.Copy)

        self.paste_action = QAction("粘贴", self)
        self.paste_action.setShortcut(QKeySequence.Paste)

        self.settings_action = QAction("设置", self)
        self.settings_action.setIcon(style.standardIcon(QStyle.SP_DialogApplyButton))
        self.settings_action.triggered.connect(self.show_settings)

        # 任务操作动作
        self.new_task_action = QAction("新建", self)
        self.new_task_action.setIcon(self.new_icon)
        self.new_task_action.triggered.connect(self.create_new_task)

        self.edit_action = QAction("编辑", self)
        self.edit_action.setIcon(self.edit_icon)
        self.edit_action.triggered.connect(self.edit_selected_task)
        self.edit_action.setEnabled(False)

        self.duplicate_action = QAction("复制", self)
        self.duplicate_action.setIcon(self.copy_icon)
        self.duplicate_action.triggered.connect(self.duplicate_selected_task)
        self.duplicate_action.setEnabled(False)

        self.delete_action = QAction("删除", self)
        self.delete_action.setIcon(self.delete_icon)
        self.delete_action.triggered.connect(self.delete_selected_task)
        self.delete_action.setEnabled(False)

        # 任务排序动作
        self.move_up_action = QAction("上移", self)
        self.move_up_action.setIcon(self.up_icon)
        self.move_up_action.triggered.connect(self.move_task_up)
        self.move_up_action.setEnabled(False)

        self.move_down_action = QAction("下移", self)
        self.move_down_action.setIcon(self.down_icon)
        self.move_down_action.triggered.connect(self.move_task_down)
        self.move_down_action.setEnabled(False)

        # 任务导入导出动作
        self.import_action = QAction("导入", self)
        self.import_action.setIcon(self.import_icon)
        self.import_action.triggered.connect(self.import_tasks)

        self.export_action = QAction("导出", self)
        self.export_action.setIcon(self.export_icon)
        self.export_action.triggered.connect(self.export_tasks)

        # 任务执行控制动作
        self.run_action = QAction("运行", self)
        self.run_action.setIcon(self.run_icon)
        self.run_action.triggered.connect(self.run_selected_task)
        self.run_action.setEnabled(False)

        self.stop_action = QAction("终止", self)
        self.stop_action.setIcon(self.stop_icon)
        self.stop_action.triggered.connect(self.stop_selected_task)
        self.stop_action.setEnabled(False)

        # 日志查看动作
        self.log_action = QAction("日志", self)
        self.log_action.setIcon(self.log_icon)
        self.log_action.triggered.connect(self.show_task_log)
        self.log_action.setEnabled(True)  # 始终启用日志按钮，因为它可以显示全局日志

        # 帮助动作
        help_icon = style.standardIcon(QStyle.SP_DialogHelpButton)
        self.help_action = QAction("帮助", self)
        self.help_action.setIcon(help_icon)
        self.help_action.triggered.connect(self.show_help)

        about_icon = style.standardIcon(QStyle.SP_MessageBoxInformation)
        self.about_action = QAction("关于", self)
        self.about_action.setIcon(about_icon)
        self.about_action.triggered.connect(self.show_about)

    def _create_menus(self):
        """创建菜单栏"""
        # 创建菜单
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件(&F)")
        file_menu.addAction(self.new_action)
        file_menu.addAction(self.open_action)
        file_menu.addAction(self.save_action)
        file_menu.addAction(self.save_as_action)
        file_menu.addSeparator()
        file_menu.addAction(self.exit_action)

        # 编辑菜单
        edit_menu = menu_bar.addMenu("编辑(&E)")
        edit_menu.addAction(self.cut_action)
        edit_menu.addAction(self.copy_action)
        edit_menu.addAction(self.paste_action)
        edit_menu.addSeparator()
        edit_menu.addAction(self.settings_action)

        # 任务菜单
        task_menu = menu_bar.addMenu("任务(&T)")
        task_menu.addAction(self.new_task_action)
        task_menu.addAction(self.edit_action)
        task_menu.addAction(self.duplicate_action)
        task_menu.addAction(self.delete_action)
        task_menu.addSeparator()

        # 添加任务管理器菜单项
        task_manager_action = QAction("管理器(&M)...", self)
        task_manager_action.triggered.connect(self.open_task_manager)
        task_menu.addAction(task_manager_action)

        # 视图菜单
        view_menu = menu_bar.addMenu("视图(&V)")

        # 添加显示/隐藏工具栏菜单项
        toggle_toolbar_action = self.main_toolbar.toggleViewAction()
        toggle_toolbar_action.setText("工具栏")
        view_menu.addAction(toggle_toolbar_action)

        # 添加显示/隐藏分类面板菜单项
        toggle_category_action = QAction("分类面板", self)
        toggle_category_action.setCheckable(True)
        toggle_category_action.setChecked(True)
        toggle_category_action.triggered.connect(self.toggle_category_panel)
        view_menu.addAction(toggle_category_action)

        # 添加分隔符
        view_menu.addSeparator()

        # 添加查看日志选项
        view_log_action = QAction("查看日志", self)
        view_log_action.setIcon(self.log_icon)
        view_log_action.triggered.connect(self.show_log_viewer)
        view_menu.addAction(view_log_action)

        # 工具菜单 - 增强功能
        tools_menu = menu_bar.addMenu("工具(&T)")

        # 数据验证
        validate_action = QAction("数据验证(&V)...", self)
        validate_action.setIcon(self.style().standardIcon(QStyle.SP_DialogApplyButton))
        validate_action.triggered.connect(self.show_data_validation)
        tools_menu.addAction(validate_action)

        # 备份管理
        backup_action = QAction("备份管理(&B)...", self)
        backup_action.setIcon(self.style().standardIcon(QStyle.SP_DriveHDIcon))
        backup_action.triggered.connect(self.show_backup_manager)
        tools_menu.addAction(backup_action)

        # 系统状态
        system_status_action = QAction("系统状态(&S)...", self)
        system_status_action.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        system_status_action.triggered.connect(self.show_system_status)
        tools_menu.addAction(system_status_action)

        tools_menu.addSeparator()

        # 高级日志查看器
        advanced_log_action = QAction("高级日志查看器(&L)...", self)
        advanced_log_action.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        advanced_log_action.triggered.connect(self.show_advanced_log_viewer)
        tools_menu.addAction(advanced_log_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助(&H)")
        help_menu.addAction(self.help_action)
        help_menu.addAction(self.about_action)

    def _create_tool_bar(self):
        """创建工具栏"""
        # 创建主工具栏
        self.main_toolbar = QToolBar("主工具栏")
        self.main_toolbar.setObjectName("main_toolbar")  # 设置对象名，避免警告
        self.main_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)  # 图标在上，文字在下
        self.main_toolbar.setIconSize(QSize(24, 24))  # 合适的图标大小
        self.main_toolbar.setMovable(False)  # 禁止移动
        self.addToolBar(self.main_toolbar)

        # 左侧按钮组 - 按照旧版的顺序，只多了任务管理器
        self.main_toolbar.addAction(self.new_task_action)
        self.main_toolbar.addAction(self.open_action)
        self.main_toolbar.addAction(self.save_action)
        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.edit_action)
        self.main_toolbar.addAction(self.duplicate_action)
        self.main_toolbar.addAction(self.delete_action)
        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.run_action)
        self.main_toolbar.addAction(self.stop_action)

        # 添加暂停按钮
        pause_action = QAction("暂停", self)
        pause_action.setIcon(self.pause_icon)
        pause_action.triggered.connect(self.pause_selected_task)
        pause_action.setEnabled(False)
        self.main_toolbar.addAction(pause_action)
        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.move_up_action)
        self.main_toolbar.addAction(self.move_down_action)
        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.import_action)
        self.main_toolbar.addAction(self.export_action)
        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.log_action)

        # 添加刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setIcon(self.refresh_icon)
        refresh_action.triggered.connect(self.refresh_task_table)
        self.main_toolbar.addAction(refresh_action)

        # 任务管理器 - 这是新增的按钮
        task_manager_icon = self.style().standardIcon(QStyle.SP_FileDialogListView)
        task_manager_action = QAction("管理器", self)
        task_manager_action.setIcon(task_manager_icon)
        task_manager_action.triggered.connect(self.open_task_manager)
        self.main_toolbar.addAction(task_manager_action)

        # 添加弹性空间，将右侧按钮推到最右边
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.main_toolbar.addWidget(spacer)

        # 右侧按钮组
        # 添加过滤按钮
        filter_action = QAction("过滤", self)
        filter_action.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        filter_action.triggered.connect(self.toggle_category_panel)
        self.main_toolbar.addAction(filter_action)

        # 添加回收站按钮
        recycle_action = QAction("回收站", self)
        recycle_action.setIcon(self.style().standardIcon(QStyle.SP_TrashIcon))
        recycle_action.triggered.connect(self.show_recycle_bin)
        self.main_toolbar.addAction(recycle_action)

        self.main_toolbar.addSeparator()

        self.main_toolbar.addAction(self.settings_action)
        self.main_toolbar.addAction(self.help_action)
        self.main_toolbar.addAction(self.about_action)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 添加状态栏项
        self.task_count_label = QLabel("任务数: 0")
        self.status_bar.addWidget(self.task_count_label)

        self.status_bar.addPermanentWidget(QLabel("就绪"))

        # 运行时间标签
        self.run_time_label = QLabel("运行时间: 00:00:00")
        self.status_bar.addPermanentWidget(self.run_time_label)

    def _create_system_tray(self):
        """创建系统托盘"""
        # 检查系统是否支持托盘
        if not QSystemTrayIcon.isSystemTrayAvailable():
            logger.warning("系统不支持系统托盘功能")
            return

        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)

        # 设置托盘图标（使用应用程序图标或默认图标）
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "resources", "icons", "app_icon.ico"
        )
        if os.path.exists(icon_path):
            app_icon = QIcon(icon_path)
            logger.info(f"系统托盘使用应用图标: {icon_path}")
        else:
            app_icon = self.style().standardIcon(QStyle.SP_ComputerIcon)
            logger.warning(f"应用图标文件不存在，使用默认图标: {icon_path}")
        self.tray_icon.setIcon(app_icon)

        # 创建托盘菜单
        tray_menu = QMenu()

        # 显示主窗口动作
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window)
        tray_menu.addAction(show_action)

        # 新建动作
        tray_menu.addAction(self.new_task_action)

        tray_menu.addSeparator()

        # 退出动作
        quit_action = QAction("退出程序", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)

        # 设置托盘菜单
        self.tray_icon.setContextMenu(tray_menu)

        # 设置托盘提示
        self.tray_icon.setToolTip("FayAutoDesk - PC端自动化工具")

        # 连接双击事件
        self.tray_icon.activated.connect(self.on_tray_icon_activated)

        # 显示托盘图标
        self.tray_icon.show()

        logger.info("系统托盘已创建")

    def show_main_window(self):
        """显示主窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
        logger.info("主窗口已显示")

    def quit_application(self):
        """退出应用程序"""
        # 隐藏托盘图标
        if hasattr(self, "tray_icon"):
            self.tray_icon.hide()

        # 退出应用程序
        QApplication.quit()
        logger.info("应用程序已退出")

    def on_tray_icon_activated(self, reason):
        """托盘图标激活事件处理"""
        if reason == QSystemTrayIcon.DoubleClick:
            # 双击托盘图标显示主窗口
            self.show_main_window()
        elif reason == QSystemTrayIcon.Trigger:
            # 单击托盘图标（在某些系统上）
            if self.isVisible():
                self.hide()
            else:
                self.show_main_window()

    def _create_central_widget(self):
        """创建中央部件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建主分割器
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.main_splitter)

        # 左侧面板 - 分类面板
        self.left_panel = QWidget()
        self.left_panel.setObjectName("leftPanel")
        self.left_panel.setMinimumWidth(200)
        self.left_panel.setMaximumWidth(300)

        # 右侧面板 - 任务面板
        self.right_panel = QWidget()

        # 添加到分割器
        self.main_splitter.addWidget(self.left_panel)
        self.main_splitter.addWidget(self.right_panel)

        # 设置初始大小比例
        self.main_splitter.setSizes([200, 1200])

        # === 左侧布局 - 分类树 ===
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)

        # 创建分类树滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 按需显示滚动条
        scroll_area.setFrameShape(QFrame.NoFrame)

        # 设置滚动区域样式，美化滚动条
        scroll_area.setStyleSheet(
            """
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: transparent;
                width: 4px;
                border-radius: 2px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 2px;
                min-height: 15px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
            QScrollBar::handle:vertical:pressed {
                background-color: #7f8c8d;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """
        )

        # 创建分类树容器widget
        category_widget = QWidget()
        self.category_layout = QVBoxLayout(category_widget)
        self.category_layout.setContentsMargins(0, 0, 0, 0)
        self.category_layout.setSpacing(2)

        # 将分类widget设置到滚动区域
        scroll_area.setWidget(category_widget)
        left_layout.addWidget(scroll_area)

        # 添加管理分类按钮
        manage_categories_button = QPushButton("管理分类")
        manage_categories_button.clicked.connect(self.manage_categories)
        left_layout.addWidget(manage_categories_button)

        # === 右侧布局 - 任务表格 ===
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # 任务表格
        self.task_table = QTableWidget(0, 10)  # 修改为10列
        self.task_table.setHorizontalHeaderLabels(
            [
                "状态",
                "序号",
                "任务ID",
                "任务名称",
                "参数",
                "计划",
                "下次运行",
                "最后运行",
                "热键",
                "分类",
            ]
        )

        # 设置表格属性
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.task_table.setSelectionMode(QTableWidget.SingleSelection)
        self.task_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.task_table.setAlternatingRowColors(True)
        self.task_table.doubleClicked.connect(self.on_task_double_clicked)
        self.task_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.task_table.customContextMenuRequested.connect(self.show_task_context_menu)
        self.task_table.itemSelectionChanged.connect(self.on_task_selection_changed)

        # 设置表头
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 状态列
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 序号列
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 任务ID列
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 任务名称列自适应宽度
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 参数列
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 计划列
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 下次运行列
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 最后运行列
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # 热键列
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # 分类列

        right_layout.addWidget(self.task_table)

    def setup_category_tree(self):
        """设置分类树 - 使用自定义可展开树结构"""
        # 清空现有内容
        for i in reversed(range(self.category_layout.count())):
            child = self.category_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 创建分类树标题和关闭按钮
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(8, 8, 8, 8)
        title_layout.setSpacing(5)
        title_layout.setAlignment(Qt.AlignVCenter)  # 垂直居中对齐

        title_label = QLabel("任务分类")
        title_label.setStyleSheet(
            """
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """
        )
        title_label.setAlignment(Qt.AlignVCenter)  # 标签垂直居中

        # 关闭按钮 - 使用蓝色小图标
        close_button = QPushButton("✕")
        close_button.setFixedSize(16, 16)
        close_button.setStyleSheet(
            """
            QPushButton {
                border: none;
                background-color: transparent;
                color: #007bff;
                font-size: 12px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #007bff;
                color: white;
            }
        """
        )
        close_button.clicked.connect(self.toggle_category_panel)

        title_layout.addWidget(title_label, 0, Qt.AlignVCenter)
        title_layout.addStretch()
        title_layout.addWidget(close_button, 0, Qt.AlignVCenter)

        title_container.setStyleSheet(
            """
            QWidget {
                background-color: #ecf0f1;
                border-radius: 4px;
                margin-bottom: 5px;
            }
        """
        )
        self.category_layout.addWidget(title_container)

        # 分类过滤输入框
        self.category_filter = QLineEdit()
        self.category_filter.setPlaceholderText("搜索分类...")
        self.category_filter.setStyleSheet(
            """
            QLineEdit {
                padding: 6px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
                margin-bottom: 5px;
            }
            QLineEdit:focus {
                border-color: #80bdff;
                outline: none;
            }
        """
        )
        self.category_filter.textChanged.connect(self.filter_categories)
        self.category_layout.addWidget(self.category_filter)

        # 创建主要分类节点
        self.create_main_categories()

        # 添加弹性空间
        self.category_layout.addStretch()

    def create_main_categories(self):
        """创建主要分类节点"""
        # 1. 内置分类 (一级节点)
        builtin_container = self.create_expandable_category("内置分类", "📁", 0)
        self.category_layout.addWidget(builtin_container)

        # 内置分类的直接子项 (二级节点) - 与旧客户端一致
        self.add_leaf_items(
            builtin_container,
            [
                ("全部", "📋", "Ctrl+Shift+A"),
                ("收藏", "⭐", "Ctrl+Shift+~"),
                ("简单任务", "📄", ""),
                ("复合任务", "📚", ""),
            ],
            1,
        )

        # 按时间分类 (内置分类下的二级分组)
        time_container = self.create_expandable_category("按时间", "📅", 1, builtin_container)
        self.add_leaf_items(
            time_container,
            [
                ("今天", "📅", ""),
                ("三天内", "📅", ""),
                ("七天内", "📅", ""),
            ],
            2,
        )

        # 按脚本分类 (内置分类下的二级分组)
        script_container = self.create_expandable_category("按脚本", "📜", 1, builtin_container)
        self.add_leaf_items(
            script_container,
            [
                ("Bat | Cmd", "⚙️", ""),
                ("PowerShell", "💻", ""),
                ("AutoHotkey", "🔧", ""),
                ("Python", "🐍", ""),
            ],
            2,
        )

        # 按状态分类 (内置分类下的二级分组)
        status_container = self.create_expandable_category("按状态", "📊", 1, builtin_container)
        self.add_leaf_items(
            status_container,
            [
                ("启用的计划", "✅", ""),
                ("禁用的计划", "❌", ""),
                ("启用的热键", "🔥", ""),
                ("禁用的热键", "🔒", ""),
            ],
            2,
        )

        # 2. 自定义分类 (一级节点) - 与旧客户端一致
        custom_container = self.create_expandable_category("自定义分类", "📁", 0)
        self.category_layout.addWidget(custom_container)

        # 自定义分类的子项 (二级节点) - 与旧客户端一致
        self.add_leaf_items(
            custom_container,
            [
                ("全部", "📋", ""),
                ("无", "⭕", ""),
                ("任务分类1", "📝", ""),
                ("任务分类2", "📊", ""),
            ],
            1,
        )

    def create_expandable_category(self, name, icon, level, parent_container=None):
        """创建可展开的分类容器"""
        # 创建主容器
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # 创建标题按钮
        header_button = QPushButton()
        indent_pixels = level * 20  # 每级缩进20像素
        header_button.setText(f"📁 {name}")
        header_button.setFixedHeight(22)  # 统一设置为22px高度
        header_button.setStyleSheet(
            f"""
            QPushButton {{
                text-align: left;
                padding: 3px 5px 3px {indent_pixels + 5}px;
                border: none;
                background-color: transparent;
                color: #2c3e50;
                font-size: 12px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e8f4fd;
                color: #1e88e5;
            }}
            QPushButton:pressed {{
                background-color: #bbdefb;
            }}
        """
        )

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 初始状态：展开
        content_widget.setVisible(True)

        # 连接点击事件
        def toggle_expanded():
            is_visible = content_widget.isVisible()
            content_widget.setVisible(not is_visible)
            # 更新图标
            new_icon = "📂" if not is_visible else "📁"
            header_button.setText(f"{new_icon} {name}")

        header_button.clicked.connect(toggle_expanded)

        # 添加到布局
        container_layout.addWidget(header_button)
        container_layout.addWidget(content_widget)

        # 如果有父容器，添加到父容器中
        if parent_container:
            # 检查父容器是否有content_layout属性
            if hasattr(parent_container, "content_layout"):
                parent_container.content_layout.addWidget(container)
            else:
                # 如果没有，尝试找到父容器的布局
                parent_layout = parent_container.layout()
                if parent_layout:
                    parent_layout.addWidget(container)

        # 存储内容布局以便后续添加子项
        container.content_layout = content_layout

        return container

    def add_leaf_items(self, parent_container, items, level):
        """添加叶子节点项目"""
        content_layout = parent_container.content_layout

        for name, icon, shortcut in items:
            button = self.create_category_button(name, icon, level, shortcut)
            button.clicked.connect(lambda checked=False, n=name: self.filter_tasks(n))
            content_layout.addWidget(button)

    def create_category_button(self, text, icon, level, shortcut=""):
        """创建分类按钮"""
        button = QPushButton()

        # 根据层级设置更明显的缩进和样式
        indent_pixels = level * 20  # 每级缩进20像素
        display_text = f"{icon} {text}"
        if shortcut:
            display_text += f" ({shortcut})"

        button.setText(display_text)

        # 根据层级设置不同的样式
        if level == 0:  # 一级分类
            button.setStyleSheet(
                f"""
                QPushButton {{
                    text-align: left;
                    padding: 8px 5px 8px {indent_pixels + 5}px;
                    border: none;
                    background-color: transparent;
                    font-size: 13px;
                    font-weight: bold;
                    color: #2c3e50;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: #e8f4fd;
                    color: #1e88e5;
                }}
                QPushButton:pressed {{
                    background-color: #d4edda;
                }}
            """
            )
            button.setFixedHeight(22)  # 统一设置为22px高度
        elif level == 1:  # 二级分类
            button.setStyleSheet(
                f"""
                QPushButton {{
                    text-align: left;
                    padding: 3px 5px 3px {indent_pixels + 5}px;
                    border: none;
                    background-color: transparent;
                    font-size: 12px;
                    color: #34495e;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: #f8f9fa;
                    color: #495057;
                }}
                QPushButton:pressed {{
                    background-color: #e9ecef;
                }}
            """
            )
            button.setFixedHeight(22)  # 统一设置为22px高度
        else:  # 三级及以下分类
            button.setStyleSheet(
                f"""
                QPushButton {{
                    text-align: left;
                    padding: 3px 5px 3px {indent_pixels + 5}px;
                    border: none;
                    background-color: transparent;
                    font-size: 11px;
                    color: #6c757d;
                    border-radius: 2px;
                }}
                QPushButton:hover {{
                    background-color: #f1f3f4;
                    color: #495057;
                }}
                QPushButton:pressed {{
                    background-color: #dee2e6;
                }}
            """
            )
            button.setFixedHeight(22)  # 统一设置为22px高度

        button.setCursor(Qt.PointingHandCursor)
        return button

    def filter_tasks(self, category_name):
        """根据分类名称过滤任务"""
        # 记住当前选中的分类
        self.current_category_id = category_name

        # 过滤任务
        self.filter_tasks_by_category(category_name, "category")

    def add_sample_categories(self):
        """添加示例分类 - 保持兼容性"""
        # 这个方法保留以保持兼容性，但实际分类已在create_main_categories中创建
        pass

    def update_category_counts(self):
        """更新分类任务计数"""
        # 统计各分类下的任务数量
        counts = {
            "all": len(self.all_tasks_data),
            "inactive": sum(1 for task in self.all_tasks_data if task.get("status") == "未运行"),
            "active": sum(1 for task in self.all_tasks_data if task.get("status") != "未运行"),
            "recent": sum(1 for task in self.all_tasks_data if task.get("last_run")),
        }

        # 注意：新的分类树不需要更新模型计数，因为我们使用的是按钮而不是模型
        # 如果需要显示计数，可以在按钮文本中添加

    def update_run_time(self):
        """更新运行时间"""
        elapsed = datetime.now() - self.start_time
        hours, remainder = divmod(int(elapsed.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"运行时间: {hours:02}:{minutes:02}:{seconds:02}"
        self.run_time_label.setText(time_str)

    # ==== 槽函数 ====
    def create_new_task(self):
        """创建新任务"""
        try:
            # 直接使用增强版任务对话框
            from .dialogs.task_dialog_enhanced import EnhancedTaskDialog

            # 获取数据库路径
            db_path = "data/fayautodesk.db"
            if hasattr(self.task_manager, "storage") and hasattr(
                self.task_manager.storage, "db_manager"
            ):
                db_path = getattr(self.task_manager.storage.db_manager, "db_path", db_path)

            # 使用增强版任务对话框创建新任务
            dialog = EnhancedTaskDialog(parent=self, task_config=None, db_path=db_path)

            # 显示对话框并获取结果
            if dialog.exec():
                # 获取任务配置
                task_config = dialog.get_task_config()

                if task_config:
                    # 添加到任务管理器
                    task_id = self.task_manager.add_task(task_config)
                    # 触发任务变更事件
                    self.task_manager.trigger_event("task_changed")

                    if task_id:
                        # 刷新任务列表
                        self.all_tasks_data = self.task_manager.get_tasks()

                        # 确保使用当前分类过滤任务
                        if hasattr(self, "current_category_id") and self.current_category_id:
                            self.filter_tasks_by_category(self.current_category_id, "category")
                        else:
                            # 如果没有当前分类，显示所有任务
                            self.update_task_table(self.all_tasks_data)

                        # 更新任务数量显示
                        self.update_task_count()

                        # 更新分类计数
                        self.update_category_counts()

                        logger.info(f"新任务创建完成: {task_config.get('name', '未命名任务')}")
                        QMessageBox.information(self, "成功", "任务创建成功！")
                    else:
                        QMessageBox.warning(self, "错误", "任务保存失败！")
                else:
                    QMessageBox.warning(self, "错误", "获取任务配置失败！")

        except Exception as e:
            logger.error(f"创建新任务时发生错误: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"创建新任务失败: {e}")

    def open_file(self):
        """打开文件"""
        QMessageBox.information(self, "提示", "打开文件功能正在开发中...")

    def save_file(self):
        """保存文件"""
        QMessageBox.information(self, "提示", "保存文件功能正在开发中...")

    def save_file_as(self):
        """另存为文件"""
        QMessageBox.information(self, "提示", "另存为功能正在开发中...")

    def edit_selected_task(self):
        """编辑选中的任务"""
        try:
            # 获取当前选中的行
            selected_items = self.task_table.selectedItems()
            if not selected_items:
                QMessageBox.information(self, "提示", "请先选择要编辑的任务！")
                return

            # 获取任务ID和配置
            row = selected_items[0].row()
            task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列获取ID
            task_config = self.task_manager.get_task_by_id(task_id)

            if not task_config:
                QMessageBox.warning(self, "错误", "无法获取任务配置信息")
                return

            # 暂时使用简化的任务对话框进行编辑，避免复杂的依赖问题
            from .dialogs.task_dialog_new import TaskDialog as TaskDialogNew

            # 使用简化的任务对话框编辑任务
            dialog = TaskDialogNew(parent=self)

            # 加载现有任务配置
            dialog.load_task_config(task_config)

            # 显示对话框并等待结果
            result = dialog.exec()

            # 处理对话框结果
            if result == QDialog.Accepted:
                # 获取更新后的任务配置
                updated_task = dialog.get_task_config()

                if updated_task:
                    # 确保保留原始ID
                    updated_task["id"] = task_id

                    # 更新任务管理器中的任务
                    # 检查任务管理器是否返回tuple格式
                    result = self.task_manager.update_task(task_id, updated_task)

                    # 处理不同的返回值格式
                    if isinstance(result, tuple):
                        success, message = result
                    else:
                        success = result
                        message = "任务更新成功" if success else "任务更新失败"

                    if success:
                        # 刷新任务列表
                        self.all_tasks_data = self.task_manager.get_tasks()

                        # 确保使用当前分类过滤任务
                        if hasattr(self, "current_category_id") and self.current_category_id:
                            self.filter_tasks_by_category(self.current_category_id, "category")
                        else:
                            # 如果没有当前分类，显示所有任务
                            self.update_task_table(self.all_tasks_data)

                        # 更新任务数量显示
                        self.update_task_count()

                        # 更新分类计数
                        self.update_category_counts()

                        logger.info(f"任务 {task_id} 编辑完成")
                        QMessageBox.information(self, "成功", "任务编辑成功！")
                    else:
                        QMessageBox.warning(self, "错误", "任务更新失败！")
                else:
                    QMessageBox.warning(self, "错误", "获取任务配置失败！")

        except Exception as e:
            logger.error(f"编辑任务时发生错误: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"编辑任务失败: {e}")

    def duplicate_selected_task(self):
        """复制选中的任务"""
        # 获取当前选中的行
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要复制的任务！")
            return

        # 获取任务ID和配置
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列获取ID
        task_config = self.task_manager.get_task_by_id(task_id)

        if task_config:
            # 复制任务
            new_task_id = self.task_manager.duplicate_task(task_id)

            if new_task_id:
                # 刷新任务列表
                self.all_tasks_data = self.task_manager.get_tasks()

                # 确保使用当前分类过滤任务
                if hasattr(self, "current_category_id") and self.current_category_id:
                    self.filter_tasks_by_category(self.current_category_id, "category")
                else:
                    # 如果没有当前分类，显示所有任务
                    self.update_task_table(self.all_tasks_data)

                # 更新任务数量显示
                self.update_task_count()

                # 更新分类计数
                self.update_category_counts()

                QMessageBox.information(self, "成功", "任务已成功复制！")
            else:
                QMessageBox.warning(self, "错误", "复制任务失败！")
        else:
            QMessageBox.warning(self, "错误", "找不到要复制的任务！")

    def delete_selected_task(self):
        """删除选中的任务"""
        # 获取当前选中的行
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要删除的任务！")
            return

        # 获取选中行的索引
        row = selected_items[0].row()

        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            "确定要删除选中的任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No,
        )

        if reply == QMessageBox.Yes:
            # 获取任务ID
            task_id = self.task_table.item(row, 3).data(Qt.UserRole)

            # 删除任务
            success = self.task_manager.delete_task(task_id)

            if success:
                # 刷新任务列表
                self.all_tasks_data = self.task_manager.get_tasks()

                # 确保使用当前分类过滤任务
                if hasattr(self, "current_category_id") and self.current_category_id:
                    self.filter_tasks_by_category(self.current_category_id, "category")
                else:
                    # 如果没有当前分类，显示所有任务
                    self.update_task_table(self.all_tasks_data)

                # 更新任务数量显示
                self.update_task_count()

                # 更新分类计数
                self.update_category_counts()

                QMessageBox.information(self, "成功", "任务已成功删除！")
            else:
                QMessageBox.warning(self, "错误", "删除任务失败！")

    def toggle_category_panel(self):
        """显示/隐藏分类面板"""
        if self.left_panel.isVisible():
            self.left_panel.hide()
        else:
            self.left_panel.show()

    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, "提示", "设置功能正在开发中...")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于FayAutoDesk",
            "FayAutoDesk - PC端自动化工具\n\n"
            "版本: 0.9.0\n"
            "开发者: FayTeam\n\n"
            "一个功能强大的PC端自动化工具，帮助您提高工作效率。",
        )

    def show_help(self):
        """显示帮助信息"""
        QMessageBox.information(self, "帮助", "帮助文档正在开发中...")

    def open_settings_dialog(self):
        """打开设置对话框（兼容性方法）"""
        self.show_settings()

    def on_category_tree_clicked(self, index):
        """分类树节点点击处理 - 保持兼容性"""
        # 这个方法保留以保持兼容性，但新的分类树使用按钮点击
        # 实际的分类点击处理在 filter_tasks 方法中
        pass

    def filter_tasks_by_category(self, category_id, category_type):
        """根据分类过滤任务"""
        logger.debug(f"过滤任务: category_id={category_id}, category_type={category_type}")

        # 如果是"全部"分类或category_id为None，显示所有任务
        if not category_id or category_id in ["全部", "all"]:
            # 使用update_task_table重新显示所有任务
            self.update_task_table(self.all_tasks_data)
            # 更新当前分类ID和类型（即使是None也保存）
            self.current_category_id = category_id
            self.current_category_type = category_type
            return

        # 保存当前分类ID和类型
        self.current_category_id = category_id
        self.current_category_type = category_type

        # 根据分类过滤任务数据
        filtered_tasks = []

        for task in self.all_tasks_data:
            show_task = False

            if category_type == "category":
                # 根据分类名称过滤
                if category_id == "收藏":
                    show_task = task.get("favorite", False)
                elif category_id == "简单任务":
                    show_task = task.get("is_simple", True)
                elif category_id == "复合任务":
                    show_task = not task.get("is_simple", True)
                elif category_id in ["今天", "三天内", "七天内"]:
                    # 时间相关过滤
                    if category_id == "今天":
                        show_task = self._is_task_today(task)
                    elif category_id == "三天内":
                        show_task = self._is_task_recent(task, 3)
                    elif category_id == "七天内":
                        show_task = self._is_task_recent(task, 7)
                elif category_id in ["Bat | Cmd", "PowerShell", "AutoHotkey", "Python"]:
                    # 脚本类型过滤
                    task_type = task.get("task_type", {})
                    parent_type = task_type.get("parent", "")
                    child_type = task_type.get("child", "")
                    if category_id == "Python":
                        show_task = (
                            "python" in parent_type.lower() or "python" in child_type.lower()
                        )
                    elif category_id == "PowerShell":
                        show_task = (
                            "powershell" in parent_type.lower()
                            or "powershell" in child_type.lower()
                        )
                    elif category_id == "Bat | Cmd":
                        show_task = "bat" in parent_type.lower() or "cmd" in parent_type.lower()
                    elif category_id == "AutoHotkey":
                        show_task = (
                            "autohotkey" in parent_type.lower() or "ahk" in parent_type.lower()
                        )
                elif category_id in ["启用的计划", "禁用的计划", "启用的热键", "禁用的热键"]:
                    # 状态过滤
                    if category_id == "启用的计划":
                        show_task = task.get("schedule") and task.get("enabled", True)
                    elif category_id == "禁用的计划":
                        show_task = task.get("schedule") and not task.get("enabled", True)
                    elif category_id == "启用的热键":
                        show_task = task.get("hotkey") and task.get("enabled", True)
                    elif category_id == "禁用的热键":
                        show_task = task.get("hotkey") and not task.get("enabled", True)
                else:
                    # 自定义分类
                    task_category = task.get("category", "")
                    show_task = task_category == category_id

            if show_task:
                filtered_tasks.append(task)

        logger.debug(f"过滤后任务数量: {len(filtered_tasks)}")

        # 更新任务表格显示过滤后的任务
        self.update_task_table(filtered_tasks)

    def _is_task_today(self, task):
        """检查任务是否是今天的"""
        last_run = task.get("last_run", "")
        if not last_run:
            return False
        try:
            from datetime import date, datetime

            # 尝试解析日期
            if isinstance(last_run, str):
                # 假设格式为 "YYYY-MM-DD HH:MM:SS" 或类似
                task_date = datetime.strptime(last_run.split()[0], "%Y-%m-%d").date()
                return task_date == date.today()
        except:
            pass
        return False

    def _is_task_recent(self, task, days):
        """检查任务是否在最近几天内"""
        last_run = task.get("last_run", "")
        if not last_run:
            return False
        try:
            from datetime import date, datetime

            # 尝试解析日期
            if isinstance(last_run, str):
                task_date = datetime.strptime(last_run.split()[0], "%Y-%m-%d").date()
                return (date.today() - task_date).days <= days
        except:
            pass
        return False

    def filter_categories(self, text):
        """过滤分类树 - 实时搜索功能"""
        logger.debug(f"过滤分类: {text}")

        # 如果搜索文本为空，显示所有分类
        if not text.strip():
            self.show_all_categories()
            return

        # 显示匹配的分类项
        search_text = text.lower().strip()
        self.search_and_show_categories(search_text)

    def show_all_categories(self):
        """显示所有分类"""
        # 显示所有主要分类容器
        for i in range(self.category_layout.count()):
            item = self.category_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, "setVisible"):
                    widget.setVisible(True)
                    # 递归显示所有子部件
                    self._show_all_widgets_recursive(widget)

    def _show_all_widgets_recursive(self, widget):
        """递归显示所有子部件"""
        if hasattr(widget, "setVisible"):
            # 不隐藏搜索框
            if not isinstance(widget, QLineEdit):
                widget.setVisible(True)

        # 递归处理子部件
        for child in widget.findChildren(QWidget):
            if child.parent() == widget:  # 只处理直接子部件
                self._show_all_widgets_recursive(child)

    def hide_all_categories(self):
        """隐藏所有分类"""
        # 隐藏主要分类容器，但保留标题和搜索框
        for i in range(self.category_layout.count()):
            item = self.category_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                # 不隐藏标题容器和搜索框
                if (
                    hasattr(widget, "setVisible")
                    and not isinstance(widget, QLineEdit)
                    and i > 1  # 跳过标题(0)和搜索框(1)
                ):
                    widget.setVisible(False)

    def show_matching_categories(self, search_text):
        """显示匹配的分类"""
        matched_widgets = []

        # 在所有分类容器中搜索匹配项
        for i in range(2, self.category_layout.count()):  # 跳过标题和搜索框
            item = self.category_layout.itemAt(i)
            if item and item.widget():
                container = item.widget()
                if self._search_in_container(container, search_text, matched_widgets):
                    container.setVisible(True)

    def _search_in_container(self, container, search_text, matched_widgets):
        """在容器中搜索匹配项"""
        has_match = False

        # 搜索容器中的所有按钮
        buttons = container.findChildren(QPushButton)
        for button in buttons:
            button_text = button.text().lower()
            # 移除图标和快捷键，只搜索分类名称
            clean_text = self._clean_button_text(button_text)

            if search_text in clean_text:
                # 找到匹配项，显示这个按钮及其所有父容器
                self._show_button_and_parents(button)
                matched_widgets.append(button)
                has_match = True

        return has_match

    def _clean_button_text(self, text):
        """清理按钮文本，移除图标和快捷键"""
        # 移除常见的图标字符
        import re

        # 移除emoji和特殊字符
        clean_text = re.sub(r"[📁📂📋⭐📄📚📅📜⚙️💻🔧🐍📊✅❌🔥🔒📝⭕]", "", text)
        # 移除快捷键部分 (括号内容)
        clean_text = re.sub(r"\([^)]*\)", "", clean_text)
        # 移除多余空格
        clean_text = " ".join(clean_text.split())
        return clean_text.strip()

    def _show_button_and_parents(self, button):
        """显示按钮及其所有父容器"""
        # 显示按钮本身
        button.setVisible(True)

        # 向上遍历，显示所有父容器
        parent = button.parent()
        while parent and parent != self.left_panel:
            if hasattr(parent, "setVisible"):
                parent.setVisible(True)

            parent = parent.parent()

        # 特别处理：确保所有可展开的容器都展开显示内容
        self._ensure_expandable_containers_visible(button)

    def _ensure_expandable_containers_visible(self, button):
        """确保可展开容器的内容可见"""
        # 向上查找所有父容器
        parent = button.parent()
        while parent and parent != self.left_panel:
            # 查找容器中的所有子部件
            children = parent.findChildren(QWidget)
            for child in children:
                # 如果子部件有content_layout属性，说明它是内容容器
                if hasattr(child, "layout") and child.parent() == parent:
                    # 检查是否有对应的header按钮
                    header_buttons = parent.findChildren(QPushButton)
                    for header_btn in header_buttons:
                        if header_btn.parent() == parent and "📁" in header_btn.text():
                            # 确保内容容器可见
                            child.setVisible(True)
                            # 更新header按钮图标为展开状态
                            current_text = header_btn.text()
                            if "📁" in current_text:
                                new_text = current_text.replace("📁", "📂")
                                header_btn.setText(new_text)
                            break
            parent = parent.parent()

    def widget_matches_search(self, widget, search_text):
        """检查widget是否匹配搜索文本 - 保持兼容性"""
        if isinstance(widget, QPushButton):
            button_text = widget.text().lower()
            clean_text = self._clean_button_text(button_text)
            return search_text in clean_text
        elif hasattr(widget, "findChildren"):
            # 检查子按钮
            buttons = widget.findChildren(QPushButton)
            for button in buttons:
                button_text = button.text().lower()
                clean_text = self._clean_button_text(button_text)
                if search_text in clean_text:
                    return True
        return False

    def show_parent_containers(self, widget):
        """显示父容器 - 保持兼容性"""
        self._show_button_and_parents(widget)

    def search_and_show_categories(self, search_text):
        """搜索并显示匹配的分类 - 智能层级显示"""
        logger.debug(f"开始搜索: '{search_text}'")

        # 首先完全隐藏所有分类
        self._hide_all_categories_completely()

        # 搜索所有按钮
        all_buttons = self.left_panel.findChildren(QPushButton)
        matched_buttons = []

        for button in all_buttons:
            # 跳过搜索框
            if button == self.category_filter:
                continue

            # 跳过没有text属性的按钮
            if not hasattr(button, "text") or not button.text():
                continue

            button_text = button.text().lower()
            clean_text = self._clean_button_text(button_text)

            # 检查是否匹配
            if search_text in clean_text:
                matched_buttons.append(button)
                logger.debug(f"找到匹配项: {clean_text} -> {button.text()}")

        logger.debug(f"共找到 {len(matched_buttons)} 个匹配按钮")

        # 智能显示匹配的分类及其必要的父级
        for button in matched_buttons:
            self._show_category_hierarchy_improved(button)

        # 如果没有匹配项，显示提示
        if not matched_buttons:
            logger.debug(f"没有找到匹配 '{search_text}' 的分类")
        else:
            logger.debug(f"搜索完成，显示了 {len(matched_buttons)} 个匹配项")

    def _show_category_hierarchy_improved(self, button):
        """改进的智能显示分类层级"""
        logger.debug(f"开始显示层级: {button.text()}")

        # 显示匹配的按钮本身
        button.setVisible(True)

        # 收集完整的父级路径
        parent_path = []
        current = button.parent()

        while current and current != self.left_panel:
            parent_path.append(current)
            current = current.parent()

        logger.debug(f"父级路径长度: {len(parent_path)}")

        # 从根到叶子显示整个路径
        for i, container in enumerate(reversed(parent_path)):
            container.setVisible(True)
            logger.debug(f"显示容器 {i}: {type(container).__name__}")

            # 查找并显示header按钮（如果有的话）
            self._find_and_show_header_button(container)

            # 显示内容容器
            self._show_content_containers(container)

    def _find_and_show_header_button(self, container):
        """查找并显示header按钮"""
        # 查找直接子按钮
        for child in container.children():
            if isinstance(child, QPushButton):
                btn_text = child.text()
                # 如果是header按钮（包含文件夹图标）
                if "📁" in btn_text or "📂" in btn_text:
                    child.setVisible(True)
                    # 更新为展开状态
                    if "📁" in btn_text:
                        new_text = btn_text.replace("📁", "📂")
                        child.setText(new_text)
                    logger.debug(f"显示并展开header按钮: {child.text()}")
                    break

    def _show_content_containers(self, container):
        """显示内容容器"""
        # 查找所有直接子部件
        for child in container.children():
            if isinstance(child, QWidget) and not isinstance(child, QPushButton):
                # 如果有layout属性，可能是内容容器
                if hasattr(child, "layout") and child.layout():
                    child.setVisible(True)
                    logger.debug(f"显示内容容器: {type(child).__name__}")

    def _hide_all_categories_completely(self):
        """完全隐藏所有分类项"""
        # 隐藏所有主要分类容器，但保留标题和搜索框
        for i in range(2, self.category_layout.count()):  # 跳过标题(0)和搜索框(1)
            item = self.category_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, "setVisible"):
                    widget.setVisible(False)
                    # 递归隐藏所有子部件
                    self._hide_all_children(widget)

    def _hide_all_children(self, widget):
        """递归隐藏所有子部件"""
        children = widget.findChildren(QWidget)
        for child in children:
            if hasattr(child, "setVisible"):
                child.setVisible(False)

    def _show_category_hierarchy(self, button):
        """智能显示分类层级 - 只显示必要的父级和匹配项"""
        # 显示匹配的按钮本身
        button.setVisible(True)
        logger.debug(f"显示匹配按钮: {button.text()}")

        # 收集从按钮到根的完整路径
        hierarchy_path = []
        current = button.parent()

        while current and current != self.left_panel:
            hierarchy_path.append(current)
            current = current.parent()

        logger.debug(f"层级路径长度: {len(hierarchy_path)}")

        # 从根到叶子显示整个路径
        for i, container in enumerate(reversed(hierarchy_path)):
            container.setVisible(True)
            logger.debug(f"显示容器 {i}: {type(container).__name__}")

            # 查找并显示header按钮
            self._show_and_expand_header(container)

            # 确保内容容器也可见
            self._ensure_content_visible(container)

    def _show_and_expand_header(self, container):
        """显示并展开header按钮"""
        # 查找直接子按钮中的header按钮
        direct_children = [
            child for child in container.children() if isinstance(child, QPushButton)
        ]

        for btn in direct_children:
            if hasattr(btn, "text") and btn.text():
                btn_text = btn.text()
                # 如果是header按钮（包含文件夹图标）
                if "📁" in btn_text or "📂" in btn_text:
                    btn.setVisible(True)
                    # 更新为展开状态
                    if "📁" in btn_text:
                        new_text = btn_text.replace("📁", "📂")
                        btn.setText(new_text)
                    logger.debug(f"显示header按钮: {btn.text()}")
                    break

    def _ensure_content_visible(self, container):
        """确保内容容器可见"""
        # 查找所有直接子部件
        direct_children = [child for child in container.children() if isinstance(child, QWidget)]

        for child in direct_children:
            # 如果不是按钮，可能是内容容器
            if not isinstance(child, QPushButton) and hasattr(child, "layout"):
                child.setVisible(True)
                logger.debug(f"显示内容容器: {type(child).__name__}")

    def _expand_header_button(self, container):
        """展开容器的header按钮"""
        # 查找直接子按钮中的header按钮
        direct_buttons = [child for child in container.children() if isinstance(child, QPushButton)]

        for btn in direct_buttons:
            if hasattr(btn, "text") and "📁" in btn.text():
                # 找到header按钮，更新为展开状态
                current_text = btn.text()
                new_text = current_text.replace("📁", "📂")
                btn.setText(new_text)
                btn.setVisible(True)

                # 查找对应的内容容器并显示
                content_containers = [
                    child
                    for child in container.children()
                    if isinstance(child, QWidget) and child != btn
                ]
                for content in content_containers:
                    if hasattr(content, "layout"):
                        content.setVisible(True)
                break

    def _show_button_and_parents_improved(self, button):
        """改进的显示按钮及其父容器的方法"""
        # 显示按钮本身
        button.setVisible(True)

        # 收集所有需要显示的父容器
        containers_to_show = []
        parent = button.parent()

        while parent and parent != self.left_panel:
            containers_to_show.append(parent)
            parent = parent.parent()

        # 从最顶层开始显示容器
        for container in reversed(containers_to_show):
            container.setVisible(True)

            # 查找并展开可折叠的容器
            self._expand_container_if_needed(container)

    def _expand_container_if_needed(self, container):
        """如果需要，展开容器"""
        # 查找容器中的header按钮（带有📁图标的按钮）
        buttons = container.findChildren(QPushButton)
        for btn in buttons:
            if btn.parent() == container and hasattr(btn, "text"):
                btn_text = btn.text()
                if "📁" in btn_text:
                    # 找到对应的内容容器
                    content_widgets = container.findChildren(QWidget)
                    for widget in content_widgets:
                        if (
                            widget.parent() == container
                            and widget != btn
                            and hasattr(widget, "layout")
                        ):
                            # 显示内容容器
                            widget.setVisible(True)
                            # 更新按钮图标为展开状态
                            new_text = btn_text.replace("📁", "📂")
                            btn.setText(new_text)
                            break
                    break

    def manage_categories(self):
        """管理分类 - 使用修复版对话框"""
        try:
            from .dialogs.category_manager_dialog_fixed import CategoryManagerDialogFixed

            # 创建并显示修复版分类管理对话框
            dialog = CategoryManagerDialogFixed(self)

            # 连接分类变更信号
            dialog.categoriesChanged.connect(self.on_categories_changed)

            # 显示对话框
            result = dialog.exec()

            # 如果用户点击了确定，刷新分类相关界面
            if result == QDialog.Accepted:
                logger.info("分类管理对话框确定，刷新界面")
                # 这里可以添加刷新分类树的逻辑
                self.statusBar().showMessage("分类已更新", 3000)

        except Exception as e:
            logger.error(f"打开分类管理对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"打开分类管理对话框失败: {e}")

    def on_categories_changed(self):
        """当分类数据发生变化时"""
        try:
            # 重新加载分类数据
            logger.info("分类数据已更改，正在刷新...")

            # 如果有分类模型，刷新它
            if hasattr(self, "category_model") and self.category_model:
                success = self.category_model.setupModelData()
                if not success:
                    logger.warning("刷新分类模型数据失败")

            # 刷新分类树UI
            self.setup_category_tree()

            # 刷新任务表格（可能需要更新分类列）
            self.refresh_task_table()

            # 更新状态栏
            self.statusBar().showMessage("分类已更新", 3000)
        except Exception as e:
            logger.error(f"刷新分类数据失败: {e}")
            self.statusBar().showMessage("刷新分类失败", 3000)

    def show_task_context_menu(self, pos):
        """显示任务上下文菜单"""
        # 获取当前选中的项
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return

        # 获取任务ID
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列（任务名称）获取ID

        # 创建菜单
        menu = QMenu(self)

        # 添加菜单项
        edit_action = menu.addAction("编辑任务")
        edit_action.triggered.connect(lambda: self.edit_task_by_id(task_id))

        execute_action = menu.addAction("执行任务")
        execute_action.triggered.connect(lambda: self.execute_task(task_id))

        duplicate_action = menu.addAction("复制任务")
        duplicate_action.triggered.connect(lambda: self.duplicate_task(task_id))

        menu.addSeparator()

        delete_action = menu.addAction("删除任务")
        delete_action.triggered.connect(lambda: self.delete_task(task_id))

        # 显示菜单
        menu.exec_(self.task_table.viewport().mapToGlobal(pos))

    def on_task_double_clicked(self, index):
        """任务双击处理"""
        # 获取任务ID
        task_id = self.task_table.item(index.row(), 3).data(
            Qt.UserRole
        )  # 从第3列（任务名称）获取ID

        # 编辑任务
        self.edit_task_by_id(task_id)

    def load_settings(self):
        """加载设置"""
        # 加载窗口位置和大小
        if self.settings.contains("geometry"):
            self.restoreGeometry(self.settings.value("geometry"))
        if self.settings.contains("windowState"):
            self.restoreState(self.settings.value("windowState"))
        if self.settings.contains("splitterSizes"):
            self.main_splitter.restoreState(self.settings.value("splitterSizes"))

    def load_sample_tasks(self):
        """加载示例任务"""
        # 创建示例任务
        sample_tasks = [
            {
                "id": str(uuid.uuid4()),
                "name": "每日提醒",
                "task_type": {"parent": "提醒", "child": "弹窗提醒"},
                "description": "这是一个每日提醒任务",
                "content": "记得喝水休息！",
                "schedule": {"type": "daily", "time": "14:00"},
            },
            {
                "id": str(uuid.uuid4()),
                "name": "定时关机",
                "task_type": {"parent": "电源", "child": "关机"},
                "description": "每天晚上11点自动关机",
                "schedule": {"type": "daily", "time": "23:00"},
            },
            {
                "id": str(uuid.uuid4()),
                "name": "网站自动备份",
                "task_type": {"parent": "程序", "child": "执行程序"},
                "description": "每周五备份网站数据",
                "schedule": {"type": "weekly", "day": "5", "time": "02:00"},
            },
        ]

        # 添加到任务管理器
        for task in sample_tasks:
            self.task_manager.add_task(task)

        # 刷新任务列表
        self.all_tasks_data = self.task_manager.get_tasks()
        self.refresh_task_table()  # 使用新方法刷新表格
        self.update_task_count()

    def update_task_count(self):
        """更新任务数量显示"""
        count = len(self.all_tasks_data)
        self.task_count_label.setText(f"任务数: {count}")

    def refresh_task_table(self):
        """刷新任务表格，显示最新任务数据"""
        # 清空表格
        self.task_table.setRowCount(0)

        # 如果没有任务数据，直接返回
        if not self.all_tasks_data:
            return

        # 添加任务到表格
        for index, task in enumerate(self.all_tasks_data):
            row_position = self.task_table.rowCount()
            self.task_table.insertRow(row_position)

            # 列0: 状态
            status = task.get("status", "未运行")
            status_item = QTableWidgetItem(status)
            if status == "运行中":
                status_item.setForeground(Qt.green)
            elif status == "执行失败":
                status_item.setForeground(Qt.red)
            self.task_table.setItem(row_position, 0, status_item)

            # 列1: 序号
            self.task_table.setItem(row_position, 1, QTableWidgetItem(str(index + 1)))

            # 列2: 任务ID
            task_id = task.get("id", "")
            self.task_table.setItem(
                row_position,
                2,
                QTableWidgetItem(task_id[:8] + "..." if len(task_id) > 8 else task_id),
            )

            # 列3: 任务名称（存储完整任务ID在用户数据中）
            name_item = QTableWidgetItem(task.get("name", "未命名"))
            name_item.setData(Qt.UserRole, task.get("id"))  # 存储任务ID
            self.task_table.setItem(row_position, 3, name_item)

            # 列4: 参数
            task_type = task.get("task_type", {})
            content = task.get("content", "")
            program_path = task.get("program_path", "")
            command = task.get("command", "")
            params = content or program_path or command or "-"
            self.task_table.setItem(
                row_position,
                4,
                QTableWidgetItem(params[:20] + "..." if len(params) > 20 else params),
            )

            # 列5: 计划
            schedule = task.get("schedule", {})
            if schedule:
                schedule_type = schedule.get("type", "")
                schedule_time = schedule.get("time", "")
                schedule_text = f"{schedule_type} {schedule_time}".strip()
            else:
                schedule_text = "无"
            self.task_table.setItem(row_position, 5, QTableWidgetItem(schedule_text))

            # 列6: 下次运行
            next_run = task.get("next_run", "")
            self.task_table.setItem(row_position, 6, QTableWidgetItem(next_run or "-"))

            # 列7: 最后运行
            last_run = task.get("last_run", "")
            self.task_table.setItem(row_position, 7, QTableWidgetItem(last_run or "-"))

            # 列8: 热键
            hotkey = task.get("hotkey", "")
            self.task_table.setItem(row_position, 8, QTableWidgetItem(hotkey or "-"))

            # 列9: 分类
            category = task.get("category", "")
            if not category:
                # 根据任务类型自动分类
                task_type = task.get("task_type", {})
                category = task_type.get("parent", "未分类")
            self.task_table.setItem(row_position, 9, QTableWidgetItem(category))

        # 更新任务数量显示
        self.update_status_text()

    def update_status_text(self):
        """更新状态栏信息"""
        # 获取任务总数
        total_tasks = len(self.all_tasks_data)

        # 获取当前显示任务数量
        displayed_tasks = self.task_table.rowCount()

        # 获取运行中任务数量
        running_tasks = sum(1 for task in self.all_tasks_data if task.get("status") == "运行中")

        # 更新状态栏
        status_text = f"任务总数: {total_tasks}"
        if displayed_tasks != total_tasks:
            status_text += f" | 当前显示: {displayed_tasks}"
        if running_tasks > 0:
            status_text += f" | 运行中: {running_tasks}"

        self.statusBar().showMessage(status_text)

    # ==== 新增的任务管理方法 ====

    def move_task_up(self):
        """上移选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要上移的任务！")
            return

        current_row = selected_items[0].row()
        if current_row == 0:
            QMessageBox.information(self, "提示", "任务已在最顶部，无法上移！")
            return

        # 获取任务ID
        task_id = self.task_table.item(current_row, 3).data(Qt.UserRole)  # 从第3列获取ID

        # 在任务管理器中移动任务
        if self.task_manager.move_task_up(task_id):
            # 刷新任务列表
            self.all_tasks_data = self.task_manager.get_tasks()
            self.refresh_task_table()

            # 重新选中移动后的任务
            self.task_table.selectRow(current_row - 1)

            self.statusBar().showMessage("任务已上移", 2000)
        else:
            QMessageBox.warning(self, "错误", "上移任务失败！")

    def move_task_down(self):
        """下移选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要下移的任务！")
            return

        current_row = selected_items[0].row()
        if current_row == self.task_table.rowCount() - 1:
            QMessageBox.information(self, "提示", "任务已在最底部，无法下移！")
            return

        # 获取任务ID
        task_id = self.task_table.item(current_row, 3).data(Qt.UserRole)  # 从第3列获取ID

        # 在任务管理器中移动任务
        if self.task_manager.move_task_down(task_id):
            # 刷新任务列表
            self.all_tasks_data = self.task_manager.get_tasks()
            self.refresh_task_table()

            # 重新选中移动后的任务
            self.task_table.selectRow(current_row + 1)

            self.statusBar().showMessage("任务已下移", 2000)
        else:
            QMessageBox.warning(self, "错误", "下移任务失败！")

    def import_tasks(self):
        """导入任务"""
        from PySide6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入任务", "", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                success_count = self.task_manager.import_tasks(file_path)
                if success_count > 0:
                    # 刷新任务列表
                    self.all_tasks_data = self.task_manager.get_tasks()
                    self.refresh_task_table()
                    self.update_task_count()
                    self.update_category_counts()

                    QMessageBox.information(self, "成功", f"成功导入 {success_count} 个任务！")
                else:
                    QMessageBox.warning(self, "警告", "没有导入任何任务！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入任务失败：{str(e)}")

    def export_tasks(self):
        """导出任务"""
        from PySide6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出任务", "tasks_export.json", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                success_count = self.task_manager.export_tasks(file_path)
                if success_count > 0:
                    QMessageBox.information(
                        self, "成功", f"成功导出 {success_count} 个任务到：\n{file_path}"
                    )
                else:
                    QMessageBox.warning(self, "警告", "没有任务可导出！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出任务失败：{str(e)}")

    def run_selected_task(self):
        """运行选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要运行的任务！")
            return

        # 获取任务ID
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列获取ID

        try:
            self.task_manager.execute_task(task_id)
            self.refresh_task_table()
            self.update_status_text()
            self.statusBar().showMessage("任务已开始运行", 3000)
        except Exception as e:
            logger.error(f"运行任务失败: {e}")
            QMessageBox.warning(self, "运行失败", f"任务运行失败: {str(e)}")

    def stop_selected_task(self):
        """终止选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要终止的任务！")
            return

        # 获取任务ID
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列获取ID

        try:
            self.task_manager.stop_task(task_id)
            self.refresh_task_table()
            self.update_status_text()
            self.statusBar().showMessage("任务已终止", 3000)
        except Exception as e:
            logger.error(f"终止任务失败: {e}")
            QMessageBox.warning(self, "终止失败", f"任务终止失败: {str(e)}")

    def pause_selected_task(self):
        """暂停选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择要暂停的任务！")
            return

        # 获取任务ID
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)

        if task_id:
            try:
                # 这里可以添加暂停任务的逻辑
                QMessageBox.information(self, "提示", "暂停功能正在开发中...")
                # self.task_manager.pause_task(task_id)
                # self.refresh_task_table()
            except Exception as e:
                logger.error(f"暂停任务失败: {e}")
                QMessageBox.warning(self, "暂停失败", f"任务暂停失败: {str(e)}")
        else:
            QMessageBox.warning(self, "错误", "无法获取任务ID！")

    def show_task_log(self):
        """显示选中任务的日志，如果没有选中任务则显示全局日志"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            # 如果没有选中任务，则显示全局日志
            self.show_log_viewer()
            return

        # 获取任务ID
        row = selected_items[0].row()
        task_id = self.task_table.item(row, 3).data(Qt.UserRole)  # 从第3列获取ID
        task_name = self.task_table.item(row, 3).text()

        try:
            # 获取任务日志
            logs = self.task_manager.get_task_logs(task_id)

            # 创建日志显示对话框
            from PySide6.QtWidgets import QDialog, QPushButton, QTextEdit, QVBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle(f"任务日志 - {task_name}")
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # 日志文本框
            log_text = QTextEdit()
            log_text.setReadOnly(True)
            log_text.setPlainText(logs if logs else "暂无日志记录")
            layout.addWidget(log_text)

            # 底部按钮布局
            button_layout = QHBoxLayout()

            # 查看全局日志按钮
            view_global_log_button = QPushButton("查看全局日志")
            view_global_log_button.clicked.connect(lambda: self.show_log_viewer())
            view_global_log_button.clicked.connect(dialog.close)

            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)

            # 添加按钮到布局
            button_layout.addWidget(view_global_log_button)
            button_layout.addStretch()
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            logger.error(f"获取任务日志失败: {e}")
            QMessageBox.warning(self, "错误", f"获取任务日志失败: {str(e)}")

    def show_log_viewer(self):
        """显示全局日志查看器"""
        try:
            # 导入日志查看器对话框
            import os as _os  # 使用别名避免重定义警告

            from .dialogs.log_viewer_dialog import LogViewerDialog

            # 尝试不同的可能日志路径，首先尝试main.py中配置的路径
            log_paths = [
                _os.path.join("data", "logs", "fayautodesk.log"),  # main.py中的默认路径
                _os.path.join("logs", "fayautodesk.log"),
                _os.path.join("fay-client", "data", "logs", "fayautodesk.log"),
                _os.path.join("fay-client", "logs", "fayautodesk.log"),
            ]

            log_file = None
            for path in log_paths:
                if _os.path.exists(path):
                    log_file = path
                    logger.debug(f"找到日志文件: {path}")
                    break
                else:
                    logger.debug(f"日志文件不存在: {path}")

            # 创建并显示日志查看器对话框
            dialog = LogViewerDialog(self, log_file)
            dialog.exec()

        except Exception as e:
            logger.error(f"显示日志查看器时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"显示日志查看器时出错: {str(e)}")

    def on_task_selection_changed(self):
        """任务选择变化时更新按钮状态"""
        selected_items = self.task_table.selectedItems()
        has_selection = len(selected_items) > 0

        # 更新需要选择任务的按钮状态
        self.edit_action.setEnabled(has_selection)
        self.duplicate_action.setEnabled(has_selection)
        self.delete_action.setEnabled(has_selection)
        self.move_up_action.setEnabled(has_selection)
        self.move_down_action.setEnabled(has_selection)
        self.run_action.setEnabled(has_selection)
        self.stop_action.setEnabled(has_selection)
        # 日志按钮始终启用，因为它可以显示全局日志
        self.log_action.setEnabled(True)

    def open_task_manager(self):
        """打开任务管理器"""
        try:
            # 导入任务管理器对话框
            from .dialogs.task_manager_dialog import TaskManagerDialog

            # 创建并显示任务管理器对话框
            task_manager_dialog = TaskManagerDialog(self, self.task_manager)
            task_manager_dialog.exec()

            # 任务管理器关闭后刷新任务列表
            self.load_tasks()
        except ImportError:
            logger.error("无法导入任务管理器对话框，请确保文件存在")
            QMessageBox.warning(self, "错误", "无法打开任务管理器，功能尚未实现")
        except Exception as e:
            logger.error(f"打开任务管理器时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"打开任务管理器时出错: {str(e)}")

    def edit_task_by_id(self, task_id):
        """根据ID编辑任务"""
        try:
            task_config = self.task_manager.get_task_by_id(task_id)
            if not task_config:
                QMessageBox.warning(self, "错误", "无法获取任务配置信息")
                return

            # 直接使用增强版任务编辑器
            from .dialogs.task_dialog_enhanced import EnhancedTaskDialog

            # 获取数据库路径
            db_path = "data/fayautodesk.db"
            if hasattr(self.task_manager, "storage") and hasattr(
                self.task_manager.storage, "db_manager"
            ):
                db_path = getattr(self.task_manager.storage.db_manager, "db_path", db_path)

            # 创建增强版任务编辑器
            dialog = EnhancedTaskDialog(parent=self, task_config=task_config, db_path=db_path)

            # 显示对话框并等待结果
            result = dialog.exec()

            # 处理对话框结果
            if result == QDialog.Accepted:
                # 获取更新后的任务配置
                updated_task = dialog.get_task_config()

                if updated_task:
                    # 确保保留原始ID
                    updated_task["id"] = task_id

                    # 更新任务管理器中的任务
                    # 检查任务管理器是否返回tuple格式
                    result = self.task_manager.update_task(task_id, updated_task)

                    # 处理不同的返回值格式
                    if isinstance(result, tuple):
                        success, message = result
                    else:
                        success = result
                        message = "任务更新成功" if success else "任务更新失败"

                    if success:
                        # 刷新任务列表
                        self.all_tasks_data = self.task_manager.get_tasks()

                        # 保存当前选中的分类，确保更新后仍然显示相同分类的任务
                        current_category_id = self.current_category_id or "全部"
                        current_category_type = getattr(self, "current_category_type", "category")

                        logger.debug(
                            f"编辑任务后使用的分类: ID={current_category_id}, 类型={current_category_type}"
                        )

                        # 使用保存的分类过滤任务
                        self.filter_tasks_by_category(current_category_id, current_category_type)

                        # 更新任务数量显示
                        self.update_task_count()

                        # 更新分类计数
                        self.update_category_counts()

                        logger.info(f"任务 {task_id} 编辑完成")
                        QMessageBox.information(self, "成功", "任务编辑成功！")
                    else:
                        QMessageBox.warning(self, "错误", "任务更新失败！")
                else:
                    QMessageBox.warning(self, "错误", "获取任务配置失败！")

        except Exception as e:
            logger.error(f"编辑任务时发生错误: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"编辑任务失败: {e}")

    def execute_task(self, task_id):
        """执行指定任务"""
        try:
            self.task_manager.execute_task(task_id)
            self.update_status_text()
            self.statusBar().showMessage(f"任务 {task_id} 正在执行", 3000)
        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            QMessageBox.warning(self, "执行失败", f"任务执行失败: {str(e)}")

    def duplicate_task(self, task_id):
        """复制指定任务"""
        task_config = self.task_manager.get_task_by_id(task_id)
        if task_config:
            # 复制任务
            new_task_id = self.task_manager.duplicate_task(task_id)

            if new_task_id:
                # 刷新任务列表
                self.all_tasks_data = self.task_manager.get_tasks()
                self.filter_tasks_by_category(self.current_category_id, "category")

                # 更新任务数量显示
                self.update_task_count()

                # 更新分类计数
                self.update_category_counts()

                QMessageBox.information(self, "成功", "任务已成功复制！")
            else:
                QMessageBox.warning(self, "错误", "复制任务失败！")
        else:
            QMessageBox.warning(self, "错误", "找不到要复制的任务！")

    def delete_task(self, task_id):
        """删除指定任务"""
        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            "确定要删除选中的任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No,
        )

        if reply == QMessageBox.Yes:
            # 删除任务
            success = self.task_manager.delete_task(task_id)
            # 触发任务变更事件
            self.task_manager.trigger_event("task_changed")

            if success:
                # 刷新任务列表
                self.all_tasks_data = self.task_manager.get_tasks()
                self.filter_tasks_by_category(self.current_category_id, "category")

                # 更新任务数量显示
                self.update_task_count()

                # 更新分类计数
                self.update_category_counts()

                QMessageBox.information(self, "成功", "任务已成功删除！")
            else:
                QMessageBox.warning(self, "错误", "删除任务失败！")

    def toggle_run_stop(self):
        """切换任务运行/停止状态（F5快捷键触发）"""
        try:
            # 获取当前选中的任务
            current_row = self.task_table.currentRow()
            if current_row >= 0:
                task_id = self.task_table.item(current_row, 3).data(Qt.UserRole)  # 从名称列获取ID
                if task_id in self.task_manager.running_tasks:
                    # 如果任务正在运行，停止它
                    self.task_manager.stop_task(task_id)
                    self.statusBar().showMessage(f"任务 {task_id} 已停止", 3000)
                else:
                    # 否则启动任务
                    self.task_manager.execute_task(task_id)
                    self.statusBar().showMessage(f"任务 {task_id} 正在执行", 3000)

                # 更新UI状态
                self.update_status_text()
                self.refresh_task_table()
            else:
                self.statusBar().showMessage("请先选择一个任务", 3000)
        except Exception as e:
            logger.error(f"切换任务运行状态失败: {e}")
            QMessageBox.warning(self, "操作失败", f"操作失败: {str(e)}")

    def on_escape_pressed(self):
        """处理Escape键按下事件"""
        # 可以实现如取消当前操作、关闭弹出窗口等功能
        self.statusBar().showMessage("已取消当前操作", 2000)

    def load_tasks(self):
        try:
            # 从任务管理器获取所有任务
            self.all_tasks_data = self.task_manager.get_tasks()

            # 如果任务数据为空，记录警告信息
            if not self.all_tasks_data:
                logger.warning("从任务管理器获取的任务数据为空")
                self.all_tasks_data = []

            # 如果已选中分类，按分类过滤
            if hasattr(self, "current_category_id") and self.current_category_id:
                self.filter_tasks_by_category(self.current_category_id, "category")
            else:
                # 否则显示所有任务
                self.update_task_table(self.all_tasks_data)

            # 更新任务数量显示
            self.update_task_count()

            # 如果任务表为空但有任务数据，强制刷新
            if self.task_table.rowCount() == 0 and len(self.all_tasks_data) > 0:
                self.update_task_table(self.all_tasks_data)

            logger.info(f"成功加载{len(self.all_tasks_data)}个任务")

            # 如果任务管理器存储模式是SQLite，则显示数据库路径
            if hasattr(self.task_manager, "storage") and hasattr(
                self.task_manager.storage, "db_manager"
            ):
                db_path = getattr(self.task_manager.storage.db_manager, "db_path", None)
                if db_path:
                    logger.info(f"任务数据库路径: {db_path}")

        except Exception as e:
            logger.error(f"加载任务失败: {e}", exc_info=True)
            self.all_tasks_data = []
            self.update_task_table([])
            # 显示错误信息到状态栏
            self.statusBar().showMessage(f"加载任务数据失败: {str(e)}", 5000)

    def create_sample_tasks(self):
        """创建示例任务"""
        # 创建几个示例任务

        # 示例任务1: 每日提醒
        daily_reminder = {
            "name": "每日喝水提醒",
            "task_type": {"parent": "提醒", "child": "弹窗提醒"},
            "description": "每小时提醒喝水",
            "content": "该喝水休息了！保持健康工作节奏。",
            "status": "未运行",
            "schedule": {"type": "hourly", "minute": 0},
        }
        self.task_manager.add_task(daily_reminder)

        # 示例任务2: 系统关机
        shutdown_task = {
            "name": "晚上定时关机",
            "task_type": {"parent": "电源", "child": "关机"},
            "description": "每天晚上11点自动关机",
            "status": "未运行",
            "schedule": {"type": "daily", "time": "23:00"},
        }
        self.task_manager.add_task(shutdown_task)

        # 示例任务3: 启动程序
        start_app_task = {
            "name": "启动工作应用",
            "task_type": {"parent": "程序", "child": "执行程序"},
            "description": "工作日早上自动启动工作应用",
            "program_path": "notepad.exe",
            "status": "未运行",
            "schedule": {"type": "workday", "time": "09:00"},
        }
        self.task_manager.add_task(start_app_task)

        # 示例任务4: 执行备份命令
        backup_task = {
            "name": "自动备份数据",
            "task_type": {"parent": "程序", "child": "运行cmd命令"},
            "description": "每周日晚自动备份重要数据",
            "command": "xcopy /E /Y D:\\重要数据 D:\\备份",
            "status": "未运行",
            "schedule": {"type": "weekly", "day": 6, "time": "22:00"},
        }
        self.task_manager.add_task(backup_task)

        logging.info("已创建4个示例任务")

    def update_task_table(self, tasks):
        """更新任务表格"""
        # 清空表格
        self.task_table.setRowCount(0)

        # 添加任务数据
        for index, task in enumerate(tasks):
            row_position = self.task_table.rowCount()
            self.task_table.insertRow(row_position)

            # 列0: 状态
            status = task.get("status", "未运行")
            status_item = QTableWidgetItem(status)
            if status == "运行中":
                status_item.setForeground(Qt.green)
            elif status == "执行失败":
                status_item.setForeground(Qt.red)
            self.task_table.setItem(row_position, 0, status_item)

            # 列1: 序号
            self.task_table.setItem(row_position, 1, QTableWidgetItem(str(index + 1)))

            # 列2: 任务ID
            task_id = task.get("id", "")
            self.task_table.setItem(
                row_position,
                2,
                QTableWidgetItem(task_id[:8] + "..." if len(task_id) > 8 else task_id),
            )

            # 列3: 任务名称（存储完整任务ID在用户数据中）
            name_item = QTableWidgetItem(task.get("name", "未命名"))
            name_item.setData(Qt.UserRole, task.get("id"))  # 存储任务ID
            self.task_table.setItem(row_position, 3, name_item)

            # 列4: 参数
            task_type = task.get("task_type", {})
            content = task.get("content", "")
            program_path = task.get("program_path", "")
            command = task.get("command", "")
            params = content or program_path or command or "-"
            self.task_table.setItem(
                row_position,
                4,
                QTableWidgetItem(params[:20] + "..." if len(params) > 20 else params),
            )

            # 列5: 计划
            schedule = task.get("schedule", {})
            if schedule:
                schedule_type = schedule.get("type", "")
                schedule_time = schedule.get("time", "")
                schedule_text = f"{schedule_type} {schedule_time}".strip()
            else:
                schedule_text = "无"
            self.task_table.setItem(row_position, 5, QTableWidgetItem(schedule_text))

            # 列6: 下次运行
            next_run = task.get("next_run", "")
            self.task_table.setItem(row_position, 6, QTableWidgetItem(next_run or "-"))

            # 列7: 最后运行
            last_run = task.get("last_run", "")
            self.task_table.setItem(row_position, 7, QTableWidgetItem(last_run or "-"))

            # 列8: 热键
            hotkey = task.get("hotkey", "")
            self.task_table.setItem(row_position, 8, QTableWidgetItem(hotkey or "-"))

            # 列9: 分类
            category = task.get("category", "")
            if not category:
                # 根据任务类型自动分类
                task_type = task.get("task_type", {})
                category = task_type.get("parent", "未分类")
            self.task_table.setItem(row_position, 9, QTableWidgetItem(category))

        # 更新任务数量显示
        self.update_status_text()

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 创建退出确认对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("退出提示")
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setText("您点击了关闭按钮，您是想：")

        # 添加操作按钮
        minimize_button = msg_box.addButton("最小化到托盘", QMessageBox.AcceptRole)
        exit_button = msg_box.addButton("立即退出程序", QMessageBox.DestructiveRole)
        cancel_button = msg_box.addButton("取消", QMessageBox.RejectRole)

        # 添加"不再询问"复选框
        dont_ask_checkbox = QCheckBox("不再询问")
        msg_box.setCheckBox(dont_ask_checkbox)

        # 设置默认按钮为最小化到托盘
        msg_box.setDefaultButton(minimize_button)

        # 显示对话框并获取结果
        msg_box.exec()
        clicked_button = msg_box.clickedButton()

        if clicked_button == minimize_button:
            # 最小化到托盘
            logger.info("用户选择最小化到托盘")
            event.ignore()  # 阻止关闭
            self.hide()  # 隐藏窗口

            # 显示托盘通知
            if hasattr(self, "tray_icon") and self.tray_icon.isVisible():
                self.tray_icon.showMessage(
                    "FayAutoDesk",
                    "程序已最小化到系统托盘，双击托盘图标可重新显示窗口",
                    QSystemTrayIcon.Information,
                    3000,
                )
            return
        elif clicked_button == exit_button:
            # 立即退出程序
            logger.info("用户选择立即退出程序")
            # 继续执行原有的关闭逻辑
        else:
            # 取消或其他情况
            logger.info("用户取消关闭操作")
            event.ignore()  # 阻止关闭
            return

        # 原有的关闭逻辑
        # 保存设置
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
        self.settings.setValue("splitterSizes", self.main_splitter.saveState())

        # 如果有未保存的更改，提示保存
        if self.has_unsaved_changes:
            reply = QMessageBox.question(
                self,
                "保存更改?",
                "有未保存的更改，是否保存?",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            )

            if reply == QMessageBox.Yes:
                self.save_file()
                if not self.has_unsaved_changes:  # 如果保存成功
                    event.accept()
                else:
                    event.ignore()
            elif reply == QMessageBox.Cancel:
                event.ignore()
            else:
                event.accept()
        else:
            event.accept()

        logger.info("应用程序关闭")

    def show_filter_dialog(self):
        """显示过滤对话框"""
        QMessageBox.information(self, "过滤", "过滤功能正在开发中...")

    def show_recycle_bin(self):
        """显示回收站"""
        QMessageBox.information(self, "回收站", "回收站功能正在开发中...")

    # ==================== 增强功能方法 ====================

    def init_enhanced_features(self):
        """初始化增强功能组件"""
        try:
            # 获取数据库路径
            self.db_path = "data/fayautodesk.db"
            if hasattr(self.task_manager, "storage") and hasattr(
                self.task_manager.storage, "db_manager"
            ):
                self.db_path = getattr(
                    self.task_manager.storage.db_manager, "db_path", self.db_path
                )

            # 使用现有的任务管理器，不重复创建
            # 如果需要增强功能，可以通过装饰器模式扩展现有的task_manager
            self.enhanced_task_manager = self.task_manager

            # 记录初始化日志
            from ui.models.log_manager_enhanced import LogCategory, LogLevel, enhanced_logger

            enhanced_logger.add_log(
                LogLevel.INFO, LogCategory.SYSTEM, "main_window", "增强功能组件初始化完成"
            )

            logger.info("增强功能组件初始化成功")

        except Exception as e:
            logger.error(f"增强功能组件初始化失败: {e}")

    def show_data_validation(self):
        """显示数据验证对话框"""
        try:
            from .dialogs.data_validation_dialog import DataValidationDialog

            dialog = DataValidationDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开数据验证对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开数据验证对话框失败: {str(e)}")

    def show_backup_manager(self):
        """显示备份管理对话框"""
        try:
            from .dialogs.backup_manager_dialog import BackupManagerDialog

            dialog = BackupManagerDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开备份管理器失败: {e}")
            QMessageBox.critical(self, "错误", f"打开备份管理器失败: {str(e)}")

    def show_system_status(self):
        """显示系统状态"""
        try:
            from .dialogs.system_status_dialog import SystemStatusDialog

            dialog = SystemStatusDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开系统状态对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开系统状态对话框失败: {str(e)}")

    def show_advanced_log_viewer(self):
        """显示高级日志查看器"""
        try:
            from .components.advanced_log_viewer import AdvancedLogViewer

            dialog = AdvancedLogViewer(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开高级日志查看器失败: {e}")
            QMessageBox.critical(self, "错误", f"打开高级日志查看器失败: {str(e)}")


# 当直接运行此模块时，创建并显示主窗口
if __name__ == "__main__":
    # 确保可以正确导入模块
    import os
    import sys

    # 获取当前脚本所在目录的父目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)

    # 将父目录添加到sys.path
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 创建应用程序
    app = QApplication(sys.argv)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())
