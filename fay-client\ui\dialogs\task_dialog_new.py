#!/usr/bin/env python

import datetime
import logging
import os
import re
import time
import uuid
from datetime import datetime as dt

import pygame
import pyttsx3
from PySide6 import QtCore, QtGui, QtWidgets

# 从子模块中使用类
QObject = QtCore.QObject
Qt = QtCore.Qt
QThread = QtCore.QThread
QTimer = QtCore.QTimer
Signal = QtCore.Signal

QAction = QtGui.QAction
QColor = QtGui.QColor
QIcon = QtGui.QIcon
QKeyEvent = QtGui.QKeyEvent
QMouseEvent = QtGui.QMouseEvent

QApplication = QtWidgets.QApplication
QCheckBox = QtWidgets.QCheckBox
QColorDialog = QtWidgets.QColorDialog
QComboBox = QtWidgets.QComboBox
QDialog = QtWidgets.QDialog
QDialogButtonBox = QtWidgets.QDialogButtonBox
QFormLayout = QtWidgets.QFormLayout
QGroupBox = QtWidgets.QGroupBox
QHBoxLayout = QtWidgets.QHBoxLayout
QLabel = QtWidgets.QLabel
QLineEdit = QtWidgets.QLineEdit
QMenu = QtWidgets.QMenu
QMessageBox = QtWidgets.QMessageBox
QPushButton = QtWidgets.QPushButton
QRadioButton = QtWidgets.QRadioButton
QSlider = QtWidgets.QSlider
QSpinBox = QtWidgets.QSpinBox
QStyle = QtWidgets.QStyle
QTabWidget = QtWidgets.QTabWidget
QTextEdit = QtWidgets.QTextEdit
QTimeEdit = QtWidgets.QTimeEdit
QTreeWidget = QtWidgets.QTreeWidget
QTreeWidgetItem = QtWidgets.QTreeWidgetItem
QVBoxLayout = QtWidgets.QVBoxLayout
QWidget = QtWidgets.QWidget


# 暂时使用占位符类
class TTSWorker(QObject):
    playback_started = Signal()
    playback_finished = Signal()
    error_occurred = Signal(str)

    def __init__(self, tts_engine, text, voice_id, rate):
        super().__init__()
        self.tts_engine = tts_engine
        self.text = text
        self.voice_id = voice_id
        self.rate = rate
        self.should_stop = False

    def run(self):
        try:
            # 设置语音和速率
            if self.voice_id:
                self.tts_engine.setProperty("voice", self.voice_id)
            self.tts_engine.setProperty("rate", self.rate)

            # 通知开始播放
            self.playback_started.emit()

            # 添加回调，以便在完成时发出信号
            def on_word(name, location, length):
                return not self.should_stop

            self.tts_engine.connect("word", on_word)

            # 实际执行TTS
            self.tts_engine.say(self.text)
            self.tts_engine.runAndWait()

            # 仅在未被停止时发送完成信号
            if not self.should_stop:
                self.playback_finished.emit()

        except Exception as e:
            self.error_occurred.emit(str(e))


class TaskTypeComboBox(QComboBox):
    taskTypeSelected = Signal(str, str)

    def __init__(self, parent=None):
        super().__init__(parent)

    def setup_task_types(self, task_types):
        self.clear()
        self.addItem("请选择任务类型", userData=["", ""])

        # 添加所有任务类型
        for parent_type, child_types in task_types.items():
            for child_type in child_types:
                self.addItem(f"{parent_type} > {child_type}", userData=[parent_type, child_type])

    def set_selected_type(self, parent_type, child_type):
        # 尝试找到匹配的项
        for i in range(self.count()):
            item_data = self.itemData(i)
            if isinstance(item_data, list) and len(item_data) >= 2:
                if item_data[0] == parent_type and item_data[1] == child_type:
                    self.setCurrentIndex(i)
                    return
        # 如果没找到，设置为第一项
        self.setCurrentIndex(0)

    def reset_selection(self):
        self.setCurrentIndex(0)


# 辅助函数
def show_color_dialog(parent, current_color, for_bg=True):
    """显示统一的颜色选择对话框

    Args:
        parent: 父窗口
        current_color: 当前颜色
        for_bg: 是否是背景颜色选择

    Returns:
        如果用户选择了颜色，返回QColor对象；否则返回None
    """
    # 获取当前颜色
    if not isinstance(current_color, QColor):
        current_color = QColor(current_color) if current_color else QColor("#FFFFFF")

    # 创建标准颜色对话框
    dialog_title = "选择背景颜色" if for_bg else "选择字体颜色"
    color_dialog = QColorDialog(current_color, parent)
    color_dialog.setWindowTitle(dialog_title)
    color_dialog.setOption(QColorDialog.ShowAlphaChannel, False)  # 不显示透明度通道

    # 尝试设置中文界面
    try:
        # 使用QTimer延迟设置，确保对话框完全加载
        def setup_chinese_labels():
            try:
                # 遍历所有标签，设置中文文本
                labels = color_dialog.findChildren(QLabel)
                for label in labels:
                    text = label.text()
                    if "Basic" in text or "基本" in text:
                        label.setText("基本颜色")
                    elif "Custom" in text or "自定义" in text:
                        label.setText("自定义颜色")
                    elif "Red" in text or text == "&Red:":
                        label.setText("红色:")
                    elif "Green" in text or text == "&Green:":
                        label.setText("绿色:")
                    elif "Blue" in text or text == "&Blue:":
                        label.setText("蓝色:")
                    elif "Hue" in text or text == "&Hue:":
                        label.setText("色调:")
                    elif "Sat" in text or text == "&Sat:":
                        label.setText("饱和度:")
                    elif "Val" in text or text == "&Val:":
                        label.setText("明度:")
                    elif "HTML" in text:
                        label.setText("HTML:")

                # 查找并设置按钮文本
                buttons = color_dialog.findChildren(QPushButton)
                for button in buttons:
                    text = button.text()
                    tooltip = button.toolTip()
                    if "OK" in text or text == "&OK":
                        button.setText("确定")
                    elif "Cancel" in text or text == "&Cancel":
                        button.setText("取消")
                    elif "Add" in text or "Add to Custom Colors" in tooltip:
                        button.setText("添加到自定义颜色")
                        button.setToolTip("添加到自定义颜色")
                    elif "Pick Screen Color" in tooltip or "screen" in text.lower():
                        button.setText("拾取屏幕颜色")
                        button.setToolTip("拾取屏幕颜色")

            except Exception as e:
                logger.debug(f"延迟设置中文标签时出错: {e}")

        # 立即设置一次
        setup_chinese_labels()

        # 延迟100ms再设置一次，确保所有控件都已加载
        QTimer.singleShot(100, setup_chinese_labels)

    except Exception as e:
        # 忽略设置中文标签的错误
        logger.debug(f"设置颜色对话框中文标签时出错: {e}")

    # 显示对话框并获取选择的颜色
    if color_dialog.exec() == QDialog.Accepted:
        color = color_dialog.selectedColor()
        if color.isValid():
            return color

    return None


def update_button_styles(bg_button, font_button, bg_color, font_color):
    """更新颜色按钮的样式

    Args:
        bg_button: 背景颜色按钮
        font_button: 字体颜色按钮
        bg_color: 背景颜色（QColor对象）
        font_color: 字体颜色（QColor对象）
    """
    if bg_button and bg_color and bg_color.isValid():
        bg_color_str = bg_color.name()
        font_color_str = font_color.name() if font_color and font_color.isValid() else "#FFFFFF"

        bg_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {bg_color_str};
                color: {font_color_str};
                border: 1px solid #5C2E0B;
                border-radius: 3px;
                padding: 4px 6px;
                text-align: center;
            }}
        """
        )

    if font_button and font_color and font_color.isValid():
        font_color_str = font_color.name()

        font_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {font_color_str};
                color: {'white' if font_color.lightness() < 128 else 'black'};
                border: 1px solid #990000;
                border-radius: 3px;
                padding: 4px 6px;
                text-align: center;
            }}
        """
        )


logger = logging.getLogger(__name__)


class TaskDialog(QDialog):
    """任务编辑对话框，包含多个标签页用于配置不同设置"""

    _mixer_initialized = False  # 类变量，跟踪 mixer 初始化状态

    def __init__(self, parent=None, task_config=None):
        super(TaskDialog, self).__init__(parent)

        # 设置窗口属性
        self.setWindowTitle("高级任务编辑器")
        self.setMinimumSize(800, 600)
        self.resize(850, 650)

        # TTS相关初始化
        self.tts_engine = None
        self.available_voices = None
        self.is_tts_busy = False  # 标识TTS引擎是否正在播放
        self.tts_thread = None  # 存储当前TTS线程
        self.tts_worker = None  # 存储当前TTS工作对象
        self.pending_new_engine_play = False  # 标识用户是否在播放时切换了引擎，需要使用新引擎重播
        self.current_playback_voice_id = None  # 存储当前播放使用的语音ID

        # 任务配置对象
        self.task_config = task_config or {}

        # 尝试初始化TTS引擎
        try:
            self.tts_engine = pyttsx3.init()
            voices = self.tts_engine.getProperty("voices")
            if voices:
                self.available_voices = voices
                logger.info(f"成功初始化TTS引擎，找到{len(voices)}个语音。")
                # 设置初始语音为第一个
                if len(voices) > 0:
                    self.tts_engine.setProperty("voice", voices[0].id)
                    logger.debug(f"设置初始语音: {voices[0].name}")
            else:
                logger.warning("TTS引擎已初始化，但未找到可用语音。")
                self.available_voices = []
        except Exception as e:
            logger.error(f"初始化TTS引擎时出错: {e}", exc_info=True)
            self.tts_engine = None
            self.available_voices = []

        # 基本属性
        self.sound_path = None  # 声音文件路径
        self.bg_color = QColor("#FF0000")  # 默认背景颜色
        self.font_color = QColor("#FFFFFF")  # 默认字体颜色

        # 任务类型数据
        self.init_task_types()

        # 初始化界面
        self.init_ui()

        # 加载任务配置（如果有）
        if task_config:
            self.load_task_config(task_config)

    def init_task_types(self):
        """初始化任务类型数据结构"""
        # 任务类型结构 - 键为一级分类，值为二级分类列表
        self.task_types = {
            "提醒": [
                "弹窗提醒",
                "任务栏通知",
                "悬浮提示",
                "报时",
                "挡屏休息",
                "倒计时提醒",
                "正计时提醒",
            ],
            "电源": [
                "关机",
                "关机对话框",
                "重启",
                "睡眠",
                "休眠",
                "注销",
                "锁定",
                "唤醒电脑",
                "网络唤醒",
                "远程关机",
            ],
            "备份": [
                "备份文件 (压缩|复制|移动)",
                "备份文件夹 (压缩|复制|移动)",
                "备份系统目录 (压缩|复制)",
                "备份注册表分支 (压缩|复制)",
                "同步文件夹",
            ],
            "音量": [
                "增加系统音量",
                "减小系统音量",
                "设置系统音量",
                "显示当前音量",
                "开启系统静音",
                "关闭系统静音",
                "切换系统静音",
                "打开音量合成器",
                "静音进程",
                "静音当前窗口",
            ],
            "窗口": [
                "最小化所有窗口|恢复",
                "关闭窗口",
                "结束窗口及其进程",
                "隐藏窗口到托盘",
                "隐藏|显示窗口",
                "激活窗口",
                "置顶窗口",
                "设置窗口位置和大小",
                "设置窗口状态(最小|最大|还原)",
                "设置窗口透明度",
                "获取窗口位置",
                "获取窗口大小",
                "获取窗口所属进程信息",
                "显示当前窗口信息",
            ],
            "显示": [
                "更换墙纸",
                "切换明暗主题色",
                "激活显示器",
                "睡眠显示器",
                "关闭|打开显示器电源",
                "调节显示器亮度",
                "切换显示器配置",
                "激活屏保",
                "关闭屏保",
                "禁用屏保",
                "启用屏保",
            ],
            "媒体": [
                "播放声音",
                "播放控制",
                "屏幕截图",
                "屏幕录像",
                "摄像头拍照",
                "摄像头录像",
                "麦克风录音",
            ],
            "程序": [
                "执行程序",
                "运行cmd命令",
                "运行PowerShell命令",
                "运行Python脚本",
                "运行AutoHotkey脚本",
                "结束进程",
                "重启进程",
                "冻结|解冻进程",
                "启动服务",
                "停止服务",
                "重启服务",
            ],
            "文件": [
                "打开文件",
                "打开文件夹",
                "关闭文件夹",
                "解压缩文件",
                "创建文件夹",
                "创建快捷方式",
                "创建符号链接",
                "删除文件",
                "删除目录",
                "重命名文件",
                "重命名目录",
                "ini文件修改",
                "ini文件读取",
                "文本文件修改",
                "文本文件读取",
                "打开文件选择框",
                "打开目录选择框",
                "获取文件大小",
                "获取文件夹大小",
                "获取文件时间",
                "获取驱动器空间",
            ],
            "系统": [
                "打开系统程序/我的电脑",
                "打开系统程序/资源管理器",
                "打开系统程序/回收站",
                "打开系统程序/控制面板",
                "打开系统程序/命令提示符",
                "打开系统程序/任务管理器",
                "打开系统程序/服务",
                "打开系统程序/程序和功能",
                "打开系统程序/设备管理器",
                "打开系统程序/计算机管理",
                "打开系统程序/事件查看器",
                "打开系统程序/远程桌面连接",
                "打开系统程序/网络和共享中心",
                "打开系统程序/网络连接",
                "打开系统程序/注册表编辑器",
                "打开系统程序/画图",
                "打开系统程序/记事本",
                "打开系统程序/截图工具",
                "打开系统程序/屏幕键盘",
                "切换任务栏自动隐藏",
                "重启资源管理器",
                "注册表修改",
                "注册表读取",
                "剪贴板写入",
                "禁用|恢复任务管理器",
                "禁用|恢复锁定电脑功能",
                "禁用|恢复注册表编辑器",
            ],
            "硬件": [
                "显示磁盘可用空间",
                "禁用触摸板",
                "启用触摸板",
                "蓝牙开关",
                "切换声音输出设备",
                "弹出驱动器",
                "切换省电配置",
                "禁用|启用硬件设备",
            ],
            "输入": [
                "输入文本",
                "发送按键",
                "发送Windows消息",
                "切换输入语言",
                "鼠标点击",
                "鼠标按下|弹起",
                "鼠标移动",
                "鼠标滚轮",
                "录制键盘鼠标动作",
            ],
            "清理": [
                "清理目录",
                "清理内存",
                "清除临时文件",
                "清理最近文档记录",
                "清空回收站",
            ],
            "网络": [
                "打开网页",
                "搜索文本",
                "禁用网络连接",
                "启用网络连接",
                "时间同步",
                "发送http请求",
                "发送邮件",
                "下载文件",
                "上传文件",
                "连接无线网络",
                "获取无线连接SSID",
                "获取无线连接名称",
            ],
            "内置": [
                "切换色彩主题",
                "开启静音模式",
                "关闭静音模式",
                "恢复热键",
                "禁用热键",
                "启用|禁用任务计划",
                "退出本软件",
                "重启本软件",
            ],
        }

        # 当前选中的任务类型
        self.current_task_type = {"parent": "", "child": ""}

    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)  # 设置适当的边距
        main_layout.setSpacing(10)  # 设置控件之间的间距

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setDocumentMode(True)  # 使用文档模式，外观更美观
        self.tab_widget.setMovable(False)  # 不允许用户移动标签

        # 创建各个标签页
        self.task_settings_tab = self.create_task_settings_tab()
        self.schedule_settings_tab = self.create_schedule_settings_tab()
        self.hotkey_settings_tab = self.create_hotkey_settings_tab()
        self.execution_settings_tab = self.create_execution_settings_tab()

        # 添加标签页到标签控件
        self.tab_widget.addTab(self.task_settings_tab, "任务设置")
        self.tab_widget.addTab(self.schedule_settings_tab, "计划设置")
        self.tab_widget.addTab(self.hotkey_settings_tab, "热键设置")
        self.tab_widget.addTab(self.execution_settings_tab, "执行设置")

        # 添加标签控件到主布局
        main_layout.addWidget(self.tab_widget)

        # 添加底部按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # 自定义按钮文本
        button_box.button(QDialogButtonBox.Ok).setText("确定")
        button_box.button(QDialogButtonBox.Cancel).setText("取消")

        main_layout.addWidget(button_box)

        # 应用对话框样式
        self.setStyleSheet(
            """
            QDialog {
                background-color: #f8f8f8;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: #ffffff;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-bottom-left-radius: 4px;
            }
            QTabWidget::tab-bar {
                left: 0px;
            }
            QTabBar::tab {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 6px 12px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom-color: #ffffff;
            }
            QTabBar::tab:hover:!selected {
                background-color: #f0f0f0;
            }
        """
        )

        # 设置对话框布局
        self.setLayout(main_layout)

        # 延迟执行初始化后的测试，确保所有控件都已完全创建
        QTimer.singleShot(100, self._post_init_test)

    def _post_init_test(self):
        """初始化后的测试方法，验证控件状态和信号连接"""
        logger.info("[DEBUG] 开始初始化后测试")

        # 检查稍后提醒相关控件是否存在
        if hasattr(self, "remind_later_check"):
            logger.info("[DEBUG] remind_later_check控件存在")

            # 检查信号连接
            signal_connections = self.remind_later_check.receivers(
                self.remind_later_check.stateChanged
            )
            logger.info(f"[DEBUG] remind_later_check信号连接数: {signal_connections}")

            # 检查相关控件的初始状态
            if hasattr(self, "remind_later_spin"):
                initial_enabled = self.remind_later_spin.isEnabled()
                logger.info(f"[DEBUG] remind_later_spin初始状态: enabled={initial_enabled}")

            if hasattr(self, "remind_later_seconds_spin"):
                initial_enabled = self.remind_later_seconds_spin.isEnabled()
                logger.info(f"[DEBUG] remind_later_seconds_spin初始状态: enabled={initial_enabled}")

            if hasattr(self, "remind_later_count_spin"):
                initial_enabled = self.remind_later_count_spin.isEnabled()
                logger.info(f"[DEBUG] remind_later_count_spin初始状态: enabled={initial_enabled}")

            # 手动触发一次信号槽测试
            self._test_manual_trigger_signal()
        else:
            logger.error("[DEBUG] remind_later_check控件不存在！")

        logger.info("[DEBUG] 初始化后测试完成")

    def create_task_settings_tab(self):
        """创建任务设置标签页，与ZTasker界面保持一致"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(12, 12, 12, 8)  # 减小边距
        layout.setSpacing(8)  # 减小主布局垂直间距

        # ===== 任务类型区域 =====
        type_form = QFormLayout()
        type_form.setSpacing(8)  # 减小表单间距
        type_form.setLabelAlignment(Qt.AlignVCenter | Qt.AlignRight)
        type_form.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)  # 允许字段自由增长
        type_form.setContentsMargins(0, 0, 0, 0)

        # 任务类型
        type_layout = QHBoxLayout()
        type_layout.setContentsMargins(0, 0, 0, 0)
        type_layout.setSpacing(5)

        # 使用可点击的文本框显示任务类型
        self.task_type_edit = ClickableLineEdit()
        self.task_type_edit.setPlaceholderText("选择任务类型")
        self.task_type_edit.setFixedHeight(26)
        self.task_type_edit.setMinimumWidth(300)
        self.task_type_edit.setObjectName("taskTypeEdit")
        # 设置点击回调，点击文本框时打开下拉菜单
        self.task_type_edit.set_click_callback(self.show_task_type_dropdown)
        type_layout.addWidget(self.task_type_edit, 1)  # 1表示伸展系数

        # 添加搜索按钮
        search_button = QPushButton()
        search_button.setIcon(
            QApplication.style().standardIcon(QStyle.SP_FileDialogContentsView)
        )  # 使用标准图标
        search_button.setFixedSize(26, 26)  # 略微增大尺寸
        search_button.setToolTip("搜索任务类型")
        search_button.setStyleSheet(
            """
            QPushButton {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                background-color: #fcfcfc;
                padding: 3px;
            }
            QPushButton:hover {
                background-color: #e8e8e8;
                border-color: #aaaaaa;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """
        )
        search_button.setObjectName("taskTypeSearchButton")  # 添加对象名称便于查找
        search_button.clicked.connect(self.open_task_type_search_dialog)  # 连接搜索对话框
        type_layout.addWidget(search_button)

        # 添加任务类型行
        task_type_label = QLabel("任务类型:")
        task_type_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        type_form.addRow(task_type_label, type_layout)

        # ===== 任务名称和分类（合并到同一行） =====
        name_category_layout = QHBoxLayout()
        name_category_layout.setContentsMargins(0, 0, 0, 0)
        name_category_layout.setSpacing(15)  # 增加间距让布局更清晰

        # 任务名称
        self.task_name_edit = QLineEdit()
        self.task_name_edit.setPlaceholderText("选填，不填将使用任务类型中的名称")
        self.task_name_edit.setFixedHeight(26)
        self.task_name_edit.setMinimumWidth(200)  # 设置最小宽度
        # 添加输入验证
        self.task_name_edit.textChanged.connect(self.validate_task_name)
        name_category_layout.addWidget(self.task_name_edit, 1)  # 1表示可伸展

        # 任务分类标签
        task_category_label = QLabel("任务分类:")
        task_category_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        name_category_layout.addWidget(task_category_label)

        # 任务分类下拉框
        self.task_category_combo = QComboBox()
        self.task_category_combo.addItems(["无", "系统", "备份", "自动化", "网络", "媒体"])
        self.task_category_combo.setFixedHeight(26)
        self.task_category_combo.setFixedWidth(120)
        name_category_layout.addWidget(self.task_category_combo)

        # 添加设置按钮
        settings_button = QPushButton()
        settings_button.setIcon(QIcon(":/icons/settings.png"))
        settings_button.setFixedSize(24, 24)
        settings_button.setToolTip("管理任务分类")
        settings_button.setStyleSheet("background-color: transparent; border: none;")
        name_category_layout.addWidget(settings_button)

        # 添加任务名称和分类行
        task_name_label = QLabel("任务名称:")
        task_name_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        type_form.addRow(task_name_label, name_category_layout)

        # 添加到主布局
        layout.addLayout(type_form)

        # ===== 内容区域 =====
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(5)

        # 内容标签
        content_label = QLabel("内容:")
        content_label.setAlignment(Qt.AlignRight | Qt.AlignTop)
        content_label.setFixedWidth(70)
        content_layout.addWidget(content_label)

        # 内容编辑区
        content_edit_layout = QVBoxLayout()
        content_edit_layout.setContentsMargins(0, 0, 0, 0)
        content_edit_layout.setSpacing(5)

        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("输入要提醒的内容")
        self.content_edit.setMinimumHeight(60)  # 减小最小高度
        self.content_edit.setMaximumHeight(100)  # 设置最大高度限制
        # 优化文本框清晰度和样式
        self.content_edit.setStyleSheet(
            """
            QTextEdit {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 4px;
                background-color: #ffffff;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 13px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid #0078d4;
                background-color: #ffffff;
            }
        """
        )
        content_edit_layout.addWidget(self.content_edit)

        # 内容编辑区右侧按钮
        content_buttons_layout = QVBoxLayout()
        content_buttons_layout.setContentsMargins(0, 0, 0, 0)
        content_buttons_layout.setSpacing(5)

        help_button = QPushButton("?")
        help_button.setFixedSize(24, 24)
        help_button.setToolTip("帮助")
        help_button.setStyleSheet("background-color: transparent; border: 1px solid #ccc;")
        content_buttons_layout.addWidget(help_button)

        settings_button2 = QPushButton()
        settings_button2.setIcon(QIcon(":/icons/settings.png"))
        settings_button2.setFixedSize(24, 24)
        settings_button2.setToolTip("内容设置")
        settings_button2.setStyleSheet("background-color: transparent; border: none;")
        content_buttons_layout.addWidget(settings_button2)

        content_buttons_layout.addStretch()

        # 组合内容编辑区和按钮
        content_edit_with_buttons = QHBoxLayout()
        content_edit_with_buttons.addLayout(content_edit_layout, 1)
        content_edit_with_buttons.addLayout(content_buttons_layout)

        content_layout.addLayout(content_edit_with_buttons, 1)
        layout.addLayout(content_layout)

        # ===== 声音设置 =====
        sound_group_box = QGroupBox("声音设置")  # 使用 GroupBox 统一管理
        sound_group_layout = QVBoxLayout()
        sound_group_layout.setSpacing(5)  # 减小声音组内部垂直间距
        sound_group_layout.setContentsMargins(8, 8, 8, 8)  # 减小内边距
        sound_group_box.setLayout(sound_group_layout)

        # --- 声音模式选择 ---
        sound_mode_layout = QHBoxLayout()
        self.play_sound_radio = QRadioButton("提醒声")
        self.tts_radio = QRadioButton("语音朗读提醒内容")
        sound_mode_layout.addWidget(self.play_sound_radio)
        sound_mode_layout.addWidget(self.tts_radio)
        sound_mode_layout.addStretch()
        sound_group_layout.addLayout(sound_mode_layout)

        # --- 提醒声设置 (默认可见) ---
        self.sound_widgets_container = QWidget()
        sound_selection_layout = QHBoxLayout(self.sound_widgets_container)
        sound_selection_layout.setContentsMargins(0, 5, 0, 0)  # 增加一点上边距
        sound_selection_layout.setSpacing(5)

        # 空白标签（对齐用）- 移除，让控件自然对齐
        # empty_label = QLabel("")
        # empty_label.setFixedWidth(70)
        # sound_selection_layout.addWidget(empty_label)

        self.sound_combo = QComboBox()
        self.sound_combo.setFixedHeight(26)
        self.sound_combo.setFixedWidth(120)
        self.load_sound_files()  # 加载声音
        sound_selection_layout.addWidget(self.sound_combo)

        play_button = QPushButton()
        play_button.setIcon(QApplication.style().standardIcon(QStyle.SP_MediaPlay))
        play_button.setFixedSize(24, 24)
        play_button.setToolTip("试听")
        play_button.setStyleSheet("background-color: transparent; border: none;")
        play_button.clicked.connect(self._play_selected_sound)
        sound_selection_layout.addWidget(play_button)

        play_time_label = QLabel("播放的时间(秒,0=一直):")
        sound_selection_layout.addWidget(play_time_label)

        self.repeat_spin = QSpinBox()
        self.repeat_spin.setRange(0, 100)
        self.repeat_spin.setValue(10)
        self.repeat_spin.setFixedHeight(26)
        self.repeat_spin.setFixedWidth(45)
        self.repeat_spin.setAlignment(Qt.AlignCenter)
        sound_selection_layout.addWidget(self.repeat_spin)

        sound_selection_layout.addStretch()
        sound_group_layout.addWidget(self.sound_widgets_container)

        # --- TTS 设置 (默认隐藏) ---
        self.tts_widgets_container = QWidget()
        tts_layout = QFormLayout(self.tts_widgets_container)  # 使用 FormLayout 对齐标签和控件
        tts_layout.setContentsMargins(0, 5, 0, 0)
        tts_layout.setSpacing(8)
        tts_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # 发声引擎
        voice_layout = QHBoxLayout()  # 使用 QHBoxLayout 包含下拉框和按钮
        self.voice_combo = QComboBox()
        self.voice_combo.setFixedHeight(26)
        if self.tts_engine and self.available_voices:
            for voice in self.available_voices:
                self.voice_combo.addItem(voice.name, voice.id)  # 显示 name, 存储 id
            self.voice_combo.currentIndexChanged.connect(self.on_voice_changed)
        else:
            self.voice_combo.addItem("无可用语音引擎")
            self.voice_combo.setEnabled(False)
        voice_layout.addWidget(self.voice_combo, 1)  # 让下拉框伸展

        # 添加试听按钮
        self.test_voice_button = QPushButton()
        # 图标将在 _update_play_button_ui 中设置
        # self.test_voice_button.setIcon(QApplication.style().standardIcon(QStyle.SP_MediaPlay))
        self.test_voice_button.setFixedSize(26, 26)
        # Tooltip将在 _update_play_button_ui 中设置
        # self.test_voice_button.setToolTip("试听选定语音")
        self.test_voice_button.clicked.connect(self.toggle_playback)  # 连接到新的处理方法
        if not (self.tts_engine and self.available_voices):
            self.test_voice_button.setEnabled(False)  # 如果没有引擎或声音，禁用按钮
        else:
            self._update_play_button_ui()  # 设置初始按钮状态
        voice_layout.addWidget(self.test_voice_button)

        tts_layout.addRow("发声引擎:", voice_layout)  # 将包含按钮的布局添加到 FormLayout

        # 语速
        tts_rate_layout = QHBoxLayout()
        self.tts_rate_slider = QSlider(Qt.Horizontal)
        self.tts_rate_slider.setRange(50, 300)  # 示例范围，pyttsx3 默认速率大约 200
        self.tts_rate_slider.setValue(180)  # 默认值
        self.tts_rate_slider.valueChanged.connect(self.on_tts_rate_changed)
        self.tts_rate_label = QLabel(f"{self.tts_rate_slider.value()}")  # 显示当前值
        self.tts_rate_label.setFixedWidth(30)
        tts_rate_layout.addWidget(self.tts_rate_slider)
        tts_rate_layout.addWidget(self.tts_rate_label)
        tts_layout.addRow("语速:", tts_rate_layout)

        sound_group_layout.addWidget(self.tts_widgets_container)
        self.tts_widgets_container.setVisible(False)  # 默认隐藏TTS控件

        # 连接单选按钮信号
        self.play_sound_radio.toggled.connect(self.toggle_sound_tts_widgets)
        self.play_sound_radio.setChecked(True)  # 默认选中提醒声

        # 将整个声音 GroupBox 添加到主布局
        layout.addWidget(sound_group_box)

        # ===== 稍后提醒设置 =====
        remind_later_layout = QHBoxLayout()
        remind_later_layout.setContentsMargins(0, 5, 0, 5)  # 减小上下边距
        remind_later_layout.setSpacing(6)  # 适中的间距

        # 空白标签（对齐用）
        empty_label2 = QLabel("")
        empty_label2.setFixedWidth(70)
        remind_later_layout.addWidget(empty_label2)

        # 稍后提醒复选框
        self.remind_later_check = QCheckBox("稍后提醒")
        # 连接信号槽
        self.remind_later_check.stateChanged.connect(self._on_later_remind_state_changed)
        remind_later_layout.addWidget(self.remind_later_check)

        # 稍后提醒时间
        self.remind_later_spin = NavigableSpinBox()
        self.remind_later_spin.setRange(0, 999)
        self.remind_later_spin.setValue(5)
        self.remind_later_spin.setFixedHeight(26)
        self.remind_later_spin.setFixedWidth(45)  # 宽度从50调整为45
        self.remind_later_spin.setAlignment(Qt.AlignCenter)  # 确保数值居中对齐
        self.remind_later_spin.setEnabled(False)  # 初始状态为禁用
        remind_later_layout.addWidget(self.remind_later_spin)

        # 分钟/秒后选择
        min_label = QLabel("分")
        min_label.setFixedWidth(20)  # 设置固定宽度
        remind_later_layout.addWidget(min_label)

        zero_label = QLabel("0")
        zero_label.setFixedWidth(15)  # 设置固定宽度
        zero_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        remind_later_layout.addWidget(zero_label)

        self.remind_later_seconds_spin = NavigableSpinBox()
        self.remind_later_seconds_spin.setRange(0, 59)
        self.remind_later_seconds_spin.setValue(0)
        self.remind_later_seconds_spin.setFixedHeight(26)
        self.remind_later_seconds_spin.setFixedWidth(45)  # 宽度从50调整为45
        self.remind_later_seconds_spin.setAlignment(Qt.AlignCenter)  # 确保数值居中对齐
        self.remind_later_seconds_spin.setEnabled(False)  # 初始状态为禁用
        remind_later_layout.addWidget(self.remind_later_seconds_spin)

        sec_label = QLabel("秒后")
        sec_label.setFixedWidth(35)  # 设置固定宽度
        remind_later_layout.addWidget(sec_label)

        # 稍后次数
        count_label = QLabel("稍后次数:")
        count_label.setFixedWidth(60)  # 设置固定宽度
        remind_later_layout.addWidget(count_label)

        self.remind_later_count_spin = QSpinBox()
        self.remind_later_count_spin.setRange(0, 999)
        self.remind_later_count_spin.setValue(10)
        self.remind_later_count_spin.setFixedHeight(26)
        self.remind_later_count_spin.setFixedWidth(45)  # 宽度从50调整为45
        self.remind_later_count_spin.setAlignment(Qt.AlignCenter)  # 确保数值居中对齐
        self.remind_later_count_spin.setEnabled(False)  # 初始状态为禁用
        remind_later_layout.addWidget(self.remind_later_count_spin)

        zero_once_label = QLabel("0=一直")
        zero_once_label.setFixedWidth(50)  # 设置固定宽度
        remind_later_layout.addWidget(zero_once_label)

        remind_later_layout.addStretch(1)
        layout.addLayout(remind_later_layout)

        # ===== 自动关闭设置 =====
        auto_close_layout = QHBoxLayout()
        auto_close_layout.setContentsMargins(0, 5, 0, 5)  # 减小上下边距
        auto_close_layout.setSpacing(6)  # 适中的间距

        # 空白标签（对齐用）
        empty_label3 = QLabel("")
        empty_label3.setFixedWidth(70)
        auto_close_layout.addWidget(empty_label3)

        # 自动关闭复选框
        self.auto_close_check = QCheckBox("自动关闭")
        # 连接信号槽
        self.auto_close_check.stateChanged.connect(self._on_auto_close_state_changed)
        auto_close_layout.addWidget(self.auto_close_check)

        # 自动关闭时间
        self.auto_close_spin = NavigableSpinBox()
        self.auto_close_spin.setRange(0, 999)
        self.auto_close_spin.setValue(0)
        self.auto_close_spin.setFixedHeight(26)
        self.auto_close_spin.setFixedWidth(45)  # 宽度从50调整为45
        self.auto_close_spin.setAlignment(Qt.AlignCenter)  # 确保数值居中对齐
        self.auto_close_spin.setEnabled(False)  # 初始状态为禁用
        auto_close_layout.addWidget(self.auto_close_spin)

        # 分钟/秒后选择
        min_label2 = QLabel("分")
        min_label2.setFixedWidth(20)  # 设置固定宽度
        auto_close_layout.addWidget(min_label2)

        sec_value_label = QLabel("30")
        sec_value_label.setFixedWidth(15)  # 设置固定宽度
        sec_value_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        auto_close_layout.addWidget(sec_value_label)

        self.auto_close_seconds_spin = NavigableSpinBox()
        self.auto_close_seconds_spin.setRange(0, 59)
        self.auto_close_seconds_spin.setValue(30)
        self.auto_close_seconds_spin.setFixedHeight(26)
        self.auto_close_seconds_spin.setFixedWidth(45)  # 宽度从50调整为45
        self.auto_close_seconds_spin.setAlignment(Qt.AlignCenter)  # 确保数值居中对齐
        self.auto_close_seconds_spin.setEnabled(False)  # 初始状态为禁用
        auto_close_layout.addWidget(self.auto_close_seconds_spin)

        auto_close_layout.addWidget(QLabel("秒后"))

        auto_close_layout.addStretch(1)
        layout.addLayout(auto_close_layout)

        # ===== 显示设置区域 (根据目标UI重构) =====
        display_settings_layout = QVBoxLayout()
        display_settings_layout.setContentsMargins(0, 6, 0, 0)  # 进一步减小顶部边距
        display_settings_layout.setSpacing(4)  # 进一步减小显示设置区域内部垂直间距

        # --- 第一行 ---
        row1 = QHBoxLayout()
        row1.setSpacing(6)  # 进一步减小行内间距，让界面更紧凑

        display_size_label = QLabel("显示大小:")
        display_size_label.setFixedWidth(70)
        display_size_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        row1.addWidget(display_size_label)

        self.size_combo = QComboBox()
        self.size_combo.addItems(["正常", "较大", "最大", "小型", "迷你"])
        self.size_combo.setFixedHeight(26)
        self.size_combo.setFixedWidth(100)
        row1.addWidget(self.size_combo)

        # row1.addStretch(1) # 添加间隔

        font_label = QLabel("提醒字号:")
        # font_label.setFixedWidth(70) # 不需要固定宽度
        font_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        row1.addWidget(font_label)

        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(
            [
                "8",
                "9",
                "10",
                "11",
                "12",
                "14",
                "16",
                "18",
                "20",
                "22",
                "24",
                "26",
                "28",
                "36",
                "48",
                "72",
            ]
        )
        self.font_size_combo.setCurrentText("12")
        self.font_size_combo.setFixedHeight(26)
        self.font_size_combo.setFixedWidth(60)
        row1.addWidget(self.font_size_combo)

        # 字体颜色按钮 (目标UI为红色)
        self.font_color = QColor("#FF0000")  # 红色
        font_color_button = QPushButton("字体颜色")  # 去除图标
        font_color_button.setObjectName("fontColorButton")
        font_color_button.setFixedHeight(26)  # 调整为与文本框相同高度
        # font_color_button.setFixedWidth(80) # 移除固定宽度，让按钮自适应内容
        font_color_button.setStyleSheet(
            """
            QPushButton {
                background-color: #FF0000; /* 红色背景 */
                color: #FFFFFF; /* 白色文字 */
                border-radius: 3px;
                border: 1px solid #990000;
                padding: 1px 6px;  /* 进一步减小垂直内边距 */
                text-align: center;
                font-size: 11px;  /* 进一步减小字体 */
            }
            QPushButton:hover {
                background-color: #CC0000; /* 悬停时稍微变暗 */
                border: 1px solid #770000;
            }
            QPushButton:pressed {
                background-color: #AA0000; /* 按下时更暗 */
            }
        """
        )
        font_color_button.setToolTip("选择字体颜色")
        font_color_button.clicked.connect(self.show_font_color_dialog)
        row1.addWidget(font_color_button)

        # 居中显示复选框
        self.center_display_check = QCheckBox("居中显示")
        self.center_display_check.setChecked(True)
        row1.addWidget(self.center_display_check)

        row1.addStretch(1)  # 行末拉伸
        display_settings_layout.addLayout(row1)

        # --- 第二行 ---
        row2 = QHBoxLayout()
        row2.setSpacing(6)  # 进一步减小行内间距，保持一致性

        position_label = QLabel("显示位置:")
        position_label.setFixedWidth(70)
        position_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        row2.addWidget(position_label)

        self.position_combo = QComboBox()
        self.position_combo.addItems(["屏幕中央", "左上角", "右上角", "左下角", "右下角", "自定义"])
        self.position_combo.setFixedHeight(26)
        self.position_combo.setFixedWidth(100)
        row2.addWidget(self.position_combo)

        # row2.addStretch(1) # 添加间隔

        # 背景颜色按钮 (目标UI为棕色)
        self.bg_color = QColor("#8B4513")  # 棕色 (SaddleBrown)
        bg_color_button = QPushButton("背景颜色")  # 去除图标
        bg_color_button.setObjectName("bgColorButton")
        bg_color_button.setFixedHeight(26)  # 调整为与文本框相同高度
        # bg_color_button.setFixedWidth(80) # 移除固定宽度，让按钮自适应内容
        bg_color_button.setStyleSheet(
            """
            QPushButton {
                background-color: #8B4513; /* 棕色背景 */
                color: #FFFFFF; /* 白色文字 */
                border: 1px solid #5C2E0B;
                border-radius: 3px;
                padding: 1px 6px;  /* 进一步减小垂直内边距 */
                text-align: center;
                font-size: 11px;  /* 进一步减小字体 */
            }
            QPushButton:hover {
                background-color: #A0522D; /* 悬停时稍微变亮 */
                border: 1px solid #4A2C17;
            }
            QPushButton:pressed {
                background-color: #654321; /* 按下时变暗 */
            }
        """
        )
        bg_color_button.setToolTip("选择背景颜色")
        bg_color_button.clicked.connect(self.show_bg_color_dialog)
        row2.addWidget(bg_color_button)

        # 置顶显示复选框
        self.top_display_check = QCheckBox("置顶显示")
        self.top_display_check.setChecked(True)
        row2.addWidget(self.top_display_check)

        # 页顶显示复选框
        self.page_top_check = QCheckBox("页顶显示")
        self.page_top_check.setChecked(True)
        row2.addWidget(self.page_top_check)

        # 还原当前窗口的焦点复选框
        self.restore_focus_check = QCheckBox("还原当前窗口的焦点")
        self.restore_focus_check.setChecked(True)
        row2.addWidget(self.restore_focus_check)

        row2.addStretch(1)  # 行末拉伸
        display_settings_layout.addLayout(row2)

        # --- 第三行 ---
        row3 = QHBoxLayout()
        row3.setSpacing(8)  # 减小行内间距

        transparency_label = QLabel("透明度:")
        transparency_label.setFixedWidth(70)
        transparency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        row3.addWidget(transparency_label)

        # 透明度滑块
        self.transparency_slider = QSlider(Qt.Horizontal)
        self.transparency_slider.setRange(0, 100)
        self.transparency_slider.setValue(0)
        self.transparency_slider.setFixedHeight(20)
        self.transparency_slider.valueChanged.connect(self.update_transparency_value)
        row3.addWidget(self.transparency_slider, 1)  # 让滑块伸展

        # 显示当前值
        self.transparency_value_label = QLabel("0")
        self.transparency_value_label.setFixedWidth(25)  # 稍微加宽
        self.transparency_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        row3.addWidget(self.transparency_value_label)

        # row3.addStretch(1) # 不需要行末拉伸，滑块已设置伸展
        display_settings_layout.addLayout(row3)

        # --- 第四行 ---
        row4 = QHBoxLayout()
        row4.setSpacing(8)  # 减小行内间距

        # 空白标签用于对齐
        mouse_label = QLabel("")
        mouse_label.setFixedWidth(70)
        row4.addWidget(mouse_label)

        # 鼠标穿透复选框
        self.mouse_penetrate_check = QCheckBox("鼠标穿透(按住Ctrl时恢复)")
        row4.addWidget(self.mouse_penetrate_check)

        row4.addStretch(1)  # 行末拉伸
        display_settings_layout.addLayout(row4)

        # 添加显示设置到主布局
        layout.addLayout(display_settings_layout)

        # 添加一个弹性空间，把控件都推到顶部
        layout.addStretch(1)

        return tab

    def update_transparency_value(self, value):
        """更新透明度显示值"""
        self.transparency_value_label.setText(str(value))

    def validate_task_name(self, text):
        """验证任务名称输入"""
        # 检查是否包含非法字符
        invalid_chars = ["<", ">", ":", '"', "|", "?", "*", "\\", "/"]
        has_invalid = any(char in text for char in invalid_chars)

        # 检查长度
        too_long = len(text) > 100

        # 显示错误信息
        if has_invalid:
            self.task_name_edit.setStyleSheet("border: 1px solid red;")
            if hasattr(self, "name_error_label"):
                self.name_error_label.setText("包含非法字符")
                self.name_error_label.setVisible(True)
        elif too_long:
            self.task_name_edit.setStyleSheet("border: 1px solid red;")
            if hasattr(self, "name_error_label"):
                self.name_error_label.setText("名称过长(最多100字符)")
                self.name_error_label.setVisible(True)
        else:
            self.task_name_edit.setStyleSheet("")
            if hasattr(self, "name_error_label"):
                self.name_error_label.setVisible(False)

        return not (has_invalid or too_long)

    def show_default_color_dialog(self):
        """显示默认颜色选择对话框"""
        # 获取当前按钮的背景色（如果有）
        sender = self.sender()
        current_color = QColor("#333333")  # 默认深灰色
        if sender and sender.styleSheet():
            # 尝试从样式表中提取背景色
            style = sender.styleSheet()
            match = re.search(r"background-color:\s*([^;]+)", style)
            if match:
                try:
                    current_color = QColor(match.group(1).strip())
                except Exception:
                    pass

        # 创建自定义颜色对话框以确保中文界面
        color_dialog = QColorDialog(current_color, self)
        color_dialog.setWindowTitle("选择背景颜色")
        color_dialog.setOption(QColorDialog.ShowAlphaChannel, False)  # 不显示透明度通道

        # 设置对话框标签的中文文本
        # 注意：这些设置可能只在某些Qt版本和系统上有效
        try:
            # 尝试设置对话框的标签文本
            basic_colors = color_dialog.findChild(QLabel, "lblBasicColors")
            if basic_colors:
                basic_colors.setText("基本颜色")

            custom_colors = color_dialog.findChild(QLabel, "lblCustomColors")
            if custom_colors:
                custom_colors.setText("自定义颜色")

            add_button = color_dialog.findChild(QPushButton, "addButton")
            if add_button:
                add_button.setText("添加到自定义颜色")

            pick_button = color_dialog.findChild(QPushButton, "btnScreenPicker")
            if pick_button:
                pick_button.setText("拾取屏幕颜色")

            # 设置颜色值标签
            red_label = color_dialog.findChild(QLabel, "lblRedValue")
            if red_label:
                red_label.setText("红色:")

            green_label = color_dialog.findChild(QLabel, "lblGreenValue")
            if green_label:
                green_label.setText("绿色:")

            blue_label = color_dialog.findChild(QLabel, "lblBlueValue")
            if blue_label:
                blue_label.setText("蓝色:")

            hue_label = color_dialog.findChild(QLabel, "lblHue")
            if hue_label:
                hue_label.setText("色调:")

            sat_label = color_dialog.findChild(QLabel, "lblSaturation")
            if sat_label:
                sat_label.setText("饱和度:")

            val_label = color_dialog.findChild(QLabel, "lblValue")
            if val_label:
                val_label.setText("明度:")

            alpha_label = color_dialog.findChild(QLabel, "lblAlpha")
            if alpha_label:
                alpha_label.setText("透明度:")

            # 设置按钮文本
            ok_button = color_dialog.findChild(QPushButton, "oklButton") or color_dialog.findChild(
                QPushButton, "okButton"
            )
            if ok_button:
                ok_button.setText("确定")

            cancel_button = color_dialog.findChild(QPushButton, "cancelButton")
            if cancel_button:
                cancel_button.setText("取消")
        except Exception as e:
            # 忽略错误，因为控件ID可能因Qt版本而异
            logger.debug(f"无法设置颜色对话框的中文标签: {e}")

        # 显示对话框并获取选择的颜色
        if color_dialog.exec() == QDialog.Accepted:
            color = color_dialog.selectedColor()
            if color.isValid():
                # 更新按钮的背景颜色
                if sender:
                    if "背景颜色" in sender.text():
                        sender.setStyleSheet(
                            f"background-color: {color.name()}; "
                            f"color: {'white' if color.lightness() < 128 else 'black'}; "
                            f"border-radius: 3px; padding: 3px;"
                        )
                    elif "字体颜色" in sender.text():
                        # 更新字体颜色
                        sender.setStyleSheet(
                            f"background-color: white; "
                            f"color: {color.name()}; "
                            f"border-radius: 3px; padding: 3px;"
                        )
                return color
        return None

    def show_font_color_dialog(self):
        """显示字体颜色选择对话框"""
        color = show_color_dialog(self, self.font_color, False)
        if color and color.isValid():
            self.font_color = color
            # 更新字体颜色按钮
            font_button = self.findChild(QPushButton, "fontColorButton")
            if font_button:
                font_color_str = self.font_color.name()
                font_button.setStyleSheet(
                    f"""
                    QPushButton {{
                        background-color: {font_color_str};
                        color: {'white' if self.font_color.lightness() < 128 else 'black'};
                        border: 1px solid #990000;
                        border-radius: 3px;
                        padding: 1px 6px;
                        text-align: center;
                        font-size: 11px;
                    }}
                """
                )

            # 同时更新背景颜色按钮的文字颜色
            bg_button = self.findChild(QPushButton, "bgColorButton")
            if bg_button:
                # 保持背景颜色按钮的背景色不变，只改变文字颜色
                bg_color_str = self.bg_color.name()
                bg_button.setStyleSheet(
                    f"""
                    QPushButton {{
                        background-color: {bg_color_str};
                        color: {font_color_str};  /* 使用字体颜色作为文字颜色 */
                        border: 1px solid #5C2E0B;
                        border-radius: 3px;
                        padding: 1px 6px;
                        text-align: center;
                        font-size: 11px;
                    }}
                    QPushButton:hover {{
                        background-color: #A0522D;
                        border: 1px solid #4A2C17;
                    }}
                    QPushButton:pressed {{
                        background-color: #654321;
                    }}
                """
                )
            return True
        return False

    def on_task_type_selected(self, parent_type, child_type):
        """处理任务类型选择事件"""
        # 更新当前选择的任务类型
        self.current_task_type["parent"] = parent_type
        self.current_task_type["child"] = child_type

        # 更新任务类型显示
        if hasattr(self, "task_type_edit"):
            self.task_type_edit.setText(f"{parent_type} > {child_type}")
            logger.debug(f"已选择任务类型: {parent_type} > {child_type}")

        # 每次选择新任务类型时，都更新任务名称为选择的子类型
        self.task_name_edit.setText(child_type)

        # 清空内容编辑框，防止上次内容残留
        if hasattr(self, "content_edit"):
            self.content_edit.clear()

        # 根据不同任务类型调整UI显示
        self.adjust_ui_by_task_type(parent_type, child_type)

    def adjust_ui_by_task_type(self, parent_type, child_type):
        """根据任务类型调整UI显示"""
        # 根据不同父类型调整UI显示

        if not hasattr(self, "content_edit"):
            # 如果编辑框不存在，无法进行调整
            return

        # 简化版本实现 - 根据任务类型调整UI
        if parent_type == "提醒":
            # 提醒类任务需要内容输入和声音设置
            self.content_edit.setPlaceholderText("输入要提醒的内容")
            self.content_edit.setVisible(True)

            # 显示声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(True)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(True)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(True)

            # 显示稍后提醒和自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(True)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(True)

        elif parent_type == "电源":
            # 电源类任务通常不需要内容输入
            self.content_edit.setPlaceholderText("可以输入操作备注（可选）")

            # 隐藏声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(False)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(False)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(False)

            # 隐藏稍后提醒和自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(False)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(False)

        elif parent_type == "程序":
            # 程序类任务可能需要路径输入
            self.content_edit.setPlaceholderText("输入要执行的程序路径或命令")
            self.content_edit.setVisible(True)

            # 隐藏声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(False)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(False)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(False)

            # 隐藏稍后提醒，显示自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(False)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(True)

        elif parent_type == "文件":
            # 文件类任务可能需要文件路径
            self.content_edit.setPlaceholderText("输入要操作的文件路径")
            self.content_edit.setVisible(True)

            # 隐藏声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(False)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(False)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(False)

            # 隐藏稍后提醒和自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(False)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(False)

        elif parent_type == "网络":
            if child_type == "打开网页":
                self.content_edit.setPlaceholderText("输入要打开的网页URL")
            elif child_type == "下载文件":
                self.content_edit.setPlaceholderText("输入要下载的文件URL和保存路径")
            else:
                self.content_edit.setPlaceholderText("输入网络操作相关参数")

            self.content_edit.setVisible(True)

            # 隐藏声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(False)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(False)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(False)

            # 隐藏稍后提醒，显示自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(False)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(True)

        else:
            # 默认设置
            self.content_edit.setPlaceholderText("输入任务相关参数")
            self.content_edit.setVisible(True)

            # 隐藏声音相关选项
            if hasattr(self, "play_sound_radio"):
                self.play_sound_radio.setVisible(False)
            if hasattr(self, "tts_radio"):
                self.tts_radio.setVisible(False)
            if hasattr(self, "sound_combo"):
                self.sound_combo.setVisible(False)

            # 隐藏稍后提醒和自动关闭选项
            if hasattr(self, "remind_later_check"):
                self.remind_later_check.setVisible(False)
            if hasattr(self, "auto_close_check"):
                self.auto_close_check.setVisible(False)

        # 调整特殊任务类型的参数设置
        if parent_type == "音量":
            # 可能需要添加音量滑块控件
            pass
        elif parent_type == "窗口" and (
            "透明度" in child_type or "位置" in child_type or "大小" in child_type
        ):
            # 可能需要添加窗口选择和数值输入控件
            pass
        elif parent_type == "显示" and "亮度" in child_type:
            # 可能需要添加亮度滑块控件
            pass

        # 根据需要，可以扩展此方法以支持更多任务类型的UI调整

    def create_schedule_settings_tab(self):
        """创建计划设置标签页，与ZTasker界面保持一致"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 启用计划复选框
        self.enable_schedule_check = QCheckBox("启用计划")
        self.enable_schedule_check.setChecked(True)
        layout.addWidget(self.enable_schedule_check)

        # 创建左侧和右侧面板的水平布局
        split_layout = QHBoxLayout()

        # 左侧的计划类型树结构
        schedule_tree_widget = QTreeWidget()
        schedule_tree_widget.setFixedWidth(180)
        schedule_tree_widget.setHeaderHidden(True)
        schedule_tree_widget.setIndentation(16)
        schedule_tree_widget.setExpandsOnDoubleClick(True)
        schedule_tree_widget.setAnimated(True)

        # 设置样式表
        schedule_tree_widget.setStyleSheet(
            """
            QTreeWidget {
                border: 1px solid #c0c0c0;
                background-color: #f9f9f9;
            }
            QTreeWidget::item {
                height: 25px;
                padding-left: 4px;
            }
            QTreeWidget::item:selected {
                background-color: #cde8ff;
                color: black;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(:/icons/collapsed.png);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(:/icons/expanded.png);
            }
        """
        )

        # 定时类计划
        timing_parent = QTreeWidgetItem(schedule_tree_widget)
        timing_parent.setText(0, "定时")
        timing_parent.setFlags(
            timing_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        timing_items = [
            "每秒",
            "每分钟",
            "每小时",
            "每天",
            "每周",
            "每月",
            "每年",
            "指定时间",
            "倒计时",
            "整点|半点",
            "Cron表达式",
        ]

        for item in timing_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            timing_parent.addChild(child)

        # 系统类计划
        system_parent = QTreeWidgetItem(schedule_tree_widget)
        system_parent.setText(0, "系统")
        system_parent.setFlags(
            system_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        system_items = [
            "系统空闲",
            "注册表监控",
            "剪贴板监控",
            "服务监测",
            "锁定|解锁",
            "开机后",
            "唤醒后",
            "关机时",
            "待机时",
        ]

        for item in system_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            system_parent.addChild(child)

        # 硬件类计划
        hardware_parent = QTreeWidgetItem(schedule_tree_widget)
        hardware_parent.setText(0, "硬件")
        hardware_parent.setFlags(
            hardware_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        hardware_items = [
            "CPU占用",
            "GPU占用",
            "内存占用",
            "磁盘占用",
            "磁盘空间",
            "电池电量",
            "笔记本盖子",
            "U盘插拔",
            "蓝牙连接",
            "摄像头运动检测",
        ]

        for item in hardware_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            hardware_parent.addChild(child)

        # 显示类计划
        display_parent = QTreeWidgetItem(schedule_tree_widget)
        display_parent.setText(0, "显示")
        display_parent.setFlags(
            display_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        display_items = ["窗口监测", "屏幕变化", "窗口变化", "显示器"]

        for item in display_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            display_parent.addChild(child)

        # 程序类计划
        program_parent = QTreeWidgetItem(schedule_tree_widget)
        program_parent.setText(0, "程序")
        program_parent.setFlags(
            program_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        program_items = ["进程监测", "程序结果", "脚本结果"]

        for item in program_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            program_parent.addChild(child)

        # 文件类计划
        file_parent = QTreeWidgetItem(schedule_tree_widget)
        file_parent.setText(0, "文件")
        file_parent.setFlags(file_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable)

        file_items = ["文件夹监控", "文件内容改变"]

        for item in file_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            file_parent.addChild(child)

        # 网络类计划
        network_parent = QTreeWidgetItem(schedule_tree_widget)
        network_parent.setText(0, "网络")
        network_parent.setFlags(
            network_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        network_items = ["网速", "互联网状态", "网络连接", "Ping", "端口", "网页监控"]

        for item in network_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            network_parent.addChild(child)

        # 内置类计划
        built_in_parent = QTreeWidgetItem(schedule_tree_widget)
        built_in_parent.setText(0, "内置")
        built_in_parent.setFlags(
            built_in_parent.flags() | Qt.ItemIsAutoTristate | Qt.ItemIsUserCheckable
        )

        built_in_items = ["启动时", "退出时"]

        for item in built_in_items:
            child = QTreeWidgetItem()
            child.setText(0, item)
            built_in_parent.addChild(child)

        # 默认展开所有项目
        schedule_tree_widget.expandAll()

        # 设置默认选中的项目
        schedule_tree_widget.setCurrentItem(timing_parent.child(0))  # 选中"每秒"

        # 添加树形控件到分割布局
        split_layout.addWidget(schedule_tree_widget)

        # 右侧设置区域
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(5, 0, 0, 0)

        # 连接树形控件的项目选择信号
        schedule_tree_widget.itemSelectionChanged.connect(
            lambda: self.on_schedule_item_selected(schedule_tree_widget, settings_layout)
        )

        # 固定周期设置区域 (默认显示"每分钟"设置)
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("固定间隔"))

        # 间隔数值选择
        self.interval_value = QSpinBox()
        self.interval_value.setRange(0, 999)
        self.interval_value.setValue(30)
        interval_layout.addWidget(self.interval_value)

        # 间隔单位选择
        self.interval_unit = QComboBox()
        self.interval_unit.addItems(["分钟", "小时", "天"])
        interval_layout.addWidget(self.interval_unit)

        interval_layout.addStretch()
        settings_layout.addLayout(interval_layout)

        # 添加一点空间
        settings_layout.addSpacing(10)

        # 系统启动和唤醒选项
        self.startup_reset = QCheckBox("开机后重置开始运行的时间(当设为开机自动时)")
        settings_layout.addWidget(self.startup_reset)

        self.resume_reset = QCheckBox("从待机(睡眠或休眠)中唤醒后重置开始运行时间")
        settings_layout.addWidget(self.resume_reset)

        self.unlock_reset = QCheckBox("从锁屏中解锁后重置开始运行时间")
        settings_layout.addWidget(self.unlock_reset)

        self.manual_reset = QCheckBox("手动从菜单状态栏改为启用后重置开始运行时间")
        settings_layout.addWidget(self.manual_reset)

        # 添加一点空间
        settings_layout.addSpacing(10)

        # 提示信息
        hint_label = QLabel("如果要在指定的时间启动，设置下面的开始的时间即可")
        hint_label.setStyleSheet("color: red;")
        settings_layout.addWidget(hint_label)

        # 添加一点空间
        settings_layout.addSpacing(10)

        # 指定开始和结束时间
        self.specific_start_time = QCheckBox("指定开始时间")
        settings_layout.addWidget(self.specific_start_time)

        # 选择具体时间
        start_time_layout = QHBoxLayout()
        start_time_layout.setContentsMargins(20, 0, 0, 0)
        self.start_time_edit = QTimeEdit()
        self.start_time_edit.setDisplayFormat("HH:mm:ss")
        self.start_time_edit.setTime(datetime.time(8, 0, 0))
        self.start_time_edit.setEnabled(False)
        start_time_layout.addWidget(self.start_time_edit)
        start_time_layout.addStretch()
        settings_layout.addLayout(start_time_layout)

        # 将开始时间复选框与时间编辑器连接
        self.specific_start_time.toggled.connect(self.start_time_edit.setEnabled)

        # 指定结束时间
        self.specific_end_time = QCheckBox("指定结束时间")
        settings_layout.addWidget(self.specific_end_time)

        # 选择具体结束时间
        end_time_layout = QHBoxLayout()
        end_time_layout.setContentsMargins(20, 0, 0, 0)
        self.end_time_edit = QTimeEdit()
        self.end_time_edit.setDisplayFormat("HH:mm:ss")
        self.end_time_edit.setTime(datetime.time(18, 0, 0))
        self.end_time_edit.setEnabled(False)
        end_time_layout.addWidget(self.end_time_edit)
        end_time_layout.addStretch()
        settings_layout.addLayout(end_time_layout)

        # 将结束时间复选框与时间编辑器连接
        self.specific_end_time.toggled.connect(self.end_time_edit.setEnabled)

        # 添加一点空间
        settings_layout.addSpacing(10)

        # 底部的链接按钮
        links_layout = QHBoxLayout()
        links_layout.addStretch()

        limit_count_link = QPushButton("限制触发次数")
        limit_count_link.setStyleSheet("border: none; text-decoration: underline; color: blue;")
        limit_count_link.setCursor(Qt.PointingHandCursor)
        links_layout.addWidget(limit_count_link)

        limit_time_link = QPushButton("限制触发时间段")
        limit_time_link.setStyleSheet("border: none; text-decoration: underline; color: blue;")
        limit_time_link.setCursor(Qt.PointingHandCursor)
        links_layout.addWidget(limit_time_link)

        settings_layout.addLayout(links_layout)

        # 添加底部空白
        settings_layout.addStretch()

        # 将设置区域添加到拆分布局，并设置伸展因子
        split_layout.addWidget(settings_widget, 1)

        # 将拆分布局添加到主布局
        layout.addLayout(split_layout)

        return tab

    def on_schedule_item_selected(self, tree_widget, settings_layout):
        """处理计划设置树形控件中的项目选择变更事件"""
        selected_items = tree_widget.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        if item.parent() is None:
            # 如果选择的是父项(大类)，则选择其第一个子项
            if item.childCount() > 0:
                tree_widget.setCurrentItem(item.child(0))
                return

        # 获取选中项目的文本和父项文本(如果有)
        item_text = item.text(0)
        parent_text = item.parent().text(0) if item.parent() else None

        # 清空所有当前的控件
        while settings_layout.count():
            child = settings_layout.takeAt(0)
            if child.widget():
                child.widget().setVisible(False)
                child.widget().deleteLater()
            elif child.layout():
                self.clear_layout(child.layout())

        # 根据不同的选择显示不同的设置界面
        if parent_text == "定时":
            if item_text == "每分钟" or item_text == "每小时" or item_text == "每天":
                # 添加固定周期设置
                interval_layout = QHBoxLayout()
                interval_layout.addWidget(QLabel("固定间隔"))

                # 间隔数值选择
                interval_value = QSpinBox()
                interval_value.setRange(0, 999)
                interval_value.setValue(30)
                interval_layout.addWidget(interval_value)

                # 间隔单位选择
                interval_unit = QComboBox()
                if item_text == "每分钟":
                    interval_unit.addItems(["分钟"])
                    interval_value.setValue(30)
                elif item_text == "每小时":
                    interval_unit.addItems(["小时"])
                    interval_value.setValue(1)
                elif item_text == "每天":
                    interval_unit.addItems(["天"])
                    interval_value.setValue(1)

                interval_layout.addWidget(interval_unit)
                interval_layout.addStretch()
                settings_layout.addLayout(interval_layout)

                # 添加启动和唤醒选项
                self.add_reset_options(settings_layout)

                # 添加指定时间选项
                self.add_time_settings(settings_layout)

            elif item_text == "Cron表达式":
                # Cron表达式设置
                cron_layout = QVBoxLayout()

                cron_label = QLabel("请输入Cron表达式 (秒 分 时 日 月 周)")
                cron_layout.addWidget(cron_label)

                cron_edit = QLineEdit()
                cron_edit.setPlaceholderText("例如: 0 */5 * * * * (每5分钟)")
                cron_layout.addWidget(cron_edit)

                help_label = QLabel("秒 0-59, 分 0-59, 时 0-23, 日 1-31, 月 1-12, 周 0-6(0是周日)")
                help_label.setStyleSheet("color: gray;")
                cron_layout.addWidget(help_label)

                settings_layout.addLayout(cron_layout)

            elif item_text == "指定时间":
                # 指定具体执行时间设置
                time_layout = QVBoxLayout()

                time_label = QLabel("设置具体执行时间:")
                time_layout.addWidget(time_label)

                time_edit = QTimeEdit()
                time_edit.setDisplayFormat("HH:mm:ss")
                time_edit.setTime(datetime.datetime.now().time())
                time_layout.addWidget(time_edit)

                settings_layout.addLayout(time_layout)

        elif parent_text == "系统":
            if item_text == "系统空闲":
                # 系统空闲设置
                idle_layout = QVBoxLayout()

                idle_time_layout = QHBoxLayout()
                idle_time_layout.addWidget(QLabel("系统空闲时间:"))

                idle_time = QSpinBox()
                idle_time.setRange(0, 60)
                idle_time.setValue(5)
                idle_time.setSuffix(" 分钟")
                idle_time_layout.addWidget(idle_time)
                idle_time_layout.addStretch()

                idle_layout.addLayout(idle_time_layout)

                cpu_threshold_layout = QHBoxLayout()
                cpu_threshold_layout.addWidget(QLabel("CPU使用率低于:"))

                cpu_threshold = QSpinBox()
                cpu_threshold.setRange(0, 99)
                cpu_threshold.setValue(10)
                cpu_threshold.setSuffix(" %")
                cpu_threshold_layout.addWidget(cpu_threshold)
                cpu_threshold_layout.addStretch()

                idle_layout.addLayout(cpu_threshold_layout)

                settings_layout.addLayout(idle_layout)

        elif parent_text == "硬件":
            if item_text == "CPU占用":
                # CPU占用条件设置
                cpu_layout = QVBoxLayout()

                cpu_condition_layout = QHBoxLayout()
                cpu_condition_layout.addWidget(QLabel("CPU使用率:"))

                cpu_condition = QComboBox()
                cpu_condition.addItems(["高于", "低于", "等于"])
                cpu_condition_layout.addWidget(cpu_condition)

                cpu_value = QSpinBox()
                cpu_value.setRange(0, 100)
                cpu_value.setValue(80)
                cpu_value.setSuffix(" %")
                cpu_condition_layout.addWidget(cpu_value)
                cpu_condition_layout.addStretch()

                cpu_layout.addLayout(cpu_condition_layout)

                duration_layout = QHBoxLayout()
                duration_layout.addWidget(QLabel("持续时间:"))

                duration = QSpinBox()
                duration.setRange(0, 3600)
                duration.setValue(60)
                duration.setSuffix(" 秒")
                duration_layout.addWidget(duration)
                duration_layout.addStretch()

                cpu_layout.addLayout(duration_layout)

                settings_layout.addLayout(cpu_layout)

        # 添加触发次数限制和触发时间段限制按钮
        self.add_limit_buttons(settings_layout)

        # 添加底部空白
        settings_layout.addStretch()

    def clear_layout(self, layout):
        """清空布局中的所有控件"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().setVisible(False)
                child.widget().deleteLater()
            elif child.layout():
                self.clear_layout(child.layout())

    def add_reset_options(self, layout):
        """添加启动和唤醒选项"""
        # 添加一点空间
        layout.addSpacing(10)

        # 系统启动和唤醒选项
        startup_reset = QCheckBox("开机后重置开始运行的时间(当设为开机自动时)")
        layout.addWidget(startup_reset)

        resume_reset = QCheckBox("从待机(睡眠或休眠)中唤醒后重置开始运行时间")
        layout.addWidget(resume_reset)

        unlock_reset = QCheckBox("从锁屏中解锁后重置开始运行时间")
        layout.addWidget(unlock_reset)

        manual_reset = QCheckBox("手动从菜单状态栏改为启用后重置开始运行时间")
        layout.addWidget(manual_reset)

    def add_time_settings(self, layout):
        """添加时间设置选项"""
        # 添加一点空间
        layout.addSpacing(10)

        # 提示信息
        hint_label = QLabel("如果要在指定的时间启动，设置下面的开始的时间即可")
        hint_label.setStyleSheet("color: red;")
        layout.addWidget(hint_label)

        # 添加一点空间
        layout.addSpacing(10)

        # 指定开始时间
        specific_start_time = QCheckBox("指定开始时间")
        layout.addWidget(specific_start_time)

        # 选择具体时间
        start_time_layout = QHBoxLayout()
        start_time_layout.setContentsMargins(20, 0, 0, 0)
        start_time_edit = QTimeEdit()
        start_time_edit.setDisplayFormat("HH:mm:ss")
        start_time_edit.setTime(datetime.time(8, 0, 0))
        start_time_edit.setEnabled(False)
        start_time_layout.addWidget(start_time_edit)
        start_time_layout.addStretch()
        layout.addLayout(start_time_layout)

        # 将开始时间复选框与时间编辑器连接
        specific_start_time.toggled.connect(start_time_edit.setEnabled)

        # 指定结束时间
        specific_end_time = QCheckBox("指定结束时间")
        layout.addWidget(specific_end_time)

        # 选择具体结束时间
        end_time_layout = QHBoxLayout()
        end_time_layout.setContentsMargins(20, 0, 0, 0)
        end_time_edit = QTimeEdit()
        end_time_edit.setDisplayFormat("HH:mm:ss")
        end_time_edit.setTime(datetime.time(18, 0, 0))
        end_time_edit.setEnabled(False)
        end_time_layout.addWidget(end_time_edit)
        end_time_layout.addStretch()
        layout.addLayout(end_time_layout)

        # 将结束时间复选框与时间编辑器连接
        specific_end_time.toggled.connect(end_time_edit.setEnabled)

    def add_limit_buttons(self, layout):
        """添加限制按钮"""
        # 添加一点空间
        layout.addSpacing(10)

        # 底部的链接按钮
        links_layout = QHBoxLayout()
        links_layout.addStretch()

        limit_count_link = QPushButton("限制触发次数")
        limit_count_link.setStyleSheet("border: none; text-decoration: underline; color: blue;")
        limit_count_link.setCursor(Qt.PointingHandCursor)
        links_layout.addWidget(limit_count_link)

        limit_time_link = QPushButton("限制触发时间段")
        limit_time_link.setStyleSheet("border: none; text-decoration: underline; color: blue;")
        limit_time_link.setCursor(Qt.PointingHandCursor)
        links_layout.addWidget(limit_time_link)

        layout.addLayout(links_layout)

    def create_hotkey_settings_tab(self):
        """创建热键设置标签页，与ZTasker界面保持一致"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 启用热键复选框
        self.enable_hotkey_check = QCheckBox("启用热键")
        self.enable_hotkey_check.setChecked(True)
        layout.addWidget(self.enable_hotkey_check)

        # 键盘热键设置区域
        keyboard_group = QGroupBox()
        keyboard_layout = QHBoxLayout(keyboard_group)

        # 使用键盘单选按钮
        self.use_keyboard_radio = QRadioButton("使用键盘")
        self.use_keyboard_radio.setChecked(True)
        keyboard_layout.addWidget(self.use_keyboard_radio)

        # 修饰键复选框
        self.ctrl_check = QCheckBox("Ctrl")
        keyboard_layout.addWidget(self.ctrl_check)

        self.alt_check = QCheckBox("Alt")
        keyboard_layout.addWidget(self.alt_check)

        self.shift_check = QCheckBox("Shift")
        keyboard_layout.addWidget(self.shift_check)

        self.win_check = QCheckBox("Win")
        keyboard_layout.addWidget(self.win_check)

        # 按键下拉框
        self.key_combo = QComboBox()
        keys = [
            "",
            "A",
            "B",
            "C",
            "D",
            "E",
            "F",
            "G",
            "H",
            "I",
            "J",
            "K",
            "L",
            "M",
            "N",
            "O",
            "P",
            "Q",
            "R",
            "S",
            "T",
            "U",
            "V",
            "W",
            "X",
            "Y",
            "Z",
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "F1",
            "F2",
            "F3",
            "F4",
            "F5",
            "F6",
            "F7",
            "F8",
            "F9",
            "F10",
            "F11",
            "F12",
        ]
        self.key_combo.addItems(keys)
        keyboard_layout.addWidget(self.key_combo)

        keyboard_layout.addStretch()
        layout.addWidget(keyboard_group)

        # 鼠标热键设置区域
        mouse_group = QGroupBox()
        mouse_layout = QHBoxLayout(mouse_group)

        # 使用鼠标单选按钮
        self.use_mouse_radio = QRadioButton("使用鼠标")
        mouse_layout.addWidget(self.use_mouse_radio)

        # 按键复选框
        self.left_button_check = QCheckBox("左键")
        mouse_layout.addWidget(self.left_button_check)

        self.middle_button_check = QCheckBox("中键")
        mouse_layout.addWidget(self.middle_button_check)

        self.right_button_check = QCheckBox("右键")
        mouse_layout.addWidget(self.right_button_check)

        # 滚轮选项
        self.wheel_check = QCheckBox("滚轮")
        mouse_layout.addWidget(self.wheel_check)

        # 滚轮方向
        self.wheel_direction = QComboBox()
        self.wheel_direction.addItems(["滚轮向上", "滚轮向下"])
        mouse_layout.addWidget(self.wheel_direction)

        mouse_layout.addStretch()
        layout.addWidget(mouse_group)

        # 确保只能选择一种模式：键盘或鼠标
        self.use_keyboard_radio.toggled.connect(
            lambda checked: self.toggle_input_mode(checked, True)
        )
        self.use_mouse_radio.toggled.connect(lambda checked: self.toggle_input_mode(checked, False))

        # 设置初始状态
        self.toggle_input_mode(True, True)

        # 生效限制区域
        limit_group = QGroupBox("生效限制")
        limit_layout = QVBoxLayout(limit_group)

        # 屏幕边缘限制
        self.screen_edge_check = QCheckBox("仅当鼠标在屏幕边缘时")
        limit_layout.addWidget(self.screen_edge_check)

        self.screen_edge_transition_check = QCheckBox("仅当鼠标在屏幕边缘过渡时")
        limit_layout.addWidget(self.screen_edge_transition_check)

        # 窗口限制
        window_limit_layout = QHBoxLayout()

        self.window_exists_check = QCheckBox("仅当窗口存在时")
        window_limit_layout.addWidget(self.window_exists_check)

        self.window_active_check = QCheckBox("仅当窗口激活时")
        window_limit_layout.addWidget(self.window_active_check)

        self.mouse_in_window_check = QCheckBox("仅当鼠标位于窗口内时")
        window_limit_layout.addWidget(self.mouse_in_window_check)

        limit_layout.addLayout(window_limit_layout)

        # 窗口标题和类名
        window_criteria_layout = QHBoxLayout()

        window_criteria_layout.addWidget(QLabel("窗口标题:"))
        self.window_title_edit = QLineEdit()
        self.window_title_edit.setPlaceholderText("窗口标题或正则表达式")
        window_criteria_layout.addWidget(self.window_title_edit)

        window_criteria_layout.addWidget(QLabel("窗口类名:"))
        self.window_class_edit = QLineEdit()
        self.window_class_edit.setPlaceholderText("窗口类名或正则表达式")
        window_criteria_layout.addWidget(self.window_class_edit)

        limit_layout.addLayout(window_criteria_layout)

        # 匹配选项
        match_options_layout = QHBoxLayout()

        self.partial_match_check = QCheckBox("部分匹配")
        match_options_layout.addWidget(self.partial_match_check)

        self.match_all_children_check = QCheckBox("匹配系统中所有子窗口")
        match_options_layout.addWidget(self.match_all_children_check)

        match_options_layout.addStretch()

        # 帮助按钮
        help_button = QPushButton("?")
        help_button.setFixedSize(24, 24)
        help_button.setToolTip("关于窗口匹配的帮助")
        match_options_layout.addWidget(help_button)

        limit_layout.addLayout(match_options_layout)

        # 进程存在限制
        process_layout = QHBoxLayout()

        self.process_exists_check = QCheckBox("仅当进程存在时")
        process_layout.addWidget(self.process_exists_check)

        process_layout.addStretch()

        self.process_name_edit = QLineEdit()
        self.process_name_edit.setPlaceholderText("进程名称")
        process_layout.addWidget(self.process_name_edit)

        # 添加按钮
        add_button = QPushButton("+")
        add_button.setFixedSize(24, 24)
        add_button.setToolTip("添加进程")
        process_layout.addWidget(add_button)

        # 帮助按钮
        help_button2 = QPushButton("?")
        help_button2.setFixedSize(24, 24)
        help_button2.setToolTip("关于进程匹配的帮助")
        process_layout.addWidget(help_button2)

        limit_layout.addLayout(process_layout)

        # 警告信息
        warning_label = QLabel(
            "以管理员身份启动时的提示：要在非管理员应用程序中生效，ZTasker必须要以管理员身份启动。ZTasker由于预防恶意软件的保护技术，热键在某些游戏中不生效"
        )
        warning_label.setStyleSheet("color: red;")
        warning_label.setWordWrap(True)
        limit_layout.addWidget(warning_label)

        layout.addWidget(limit_group)

        # 添加底部空白
        layout.addStretch()

        return tab

    def toggle_input_mode(self, checked, is_keyboard):
        """切换输入模式，禁用或启用相关控件"""
        # 键盘相关控件
        self.ctrl_check.setEnabled(is_keyboard and checked)
        self.alt_check.setEnabled(is_keyboard and checked)
        self.shift_check.setEnabled(is_keyboard and checked)
        self.win_check.setEnabled(is_keyboard and checked)
        self.key_combo.setEnabled(is_keyboard and checked)

        # 鼠标相关控件
        self.left_button_check.setEnabled(not is_keyboard and checked)
        self.middle_button_check.setEnabled(not is_keyboard and checked)
        self.right_button_check.setEnabled(not is_keyboard and checked)
        self.wheel_check.setEnabled(not is_keyboard and checked)
        self.wheel_direction.setEnabled(not is_keyboard and checked)

    def create_execution_settings_tab(self):
        """创建执行设置标签页，与ZTasker界面保持一致"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 条件设置区域
        condition_group = QVBoxLayout()

        # 标题提示
        condition_label = QLabel("计划触发时如需符合以下条件才能进行")
        condition_group.addWidget(condition_label)

        # 上一个任务正在运行选项
        self.prior_task_check = QCheckBox("上一个任务正在运行，在本任务完成")
        self.prior_task_check.setChecked(True)
        condition_group.addWidget(self.prior_task_check)

        # 全屏应用选项
        self.fullscreen_check = QCheckBox("有全屏程序正在运行")
        condition_group.addWidget(self.fullscreen_check)

        # 系统初始化阶段选项
        self.system_init_check = QCheckBox("正处于活动初始化阶段(系统需要>=Windows10)")
        condition_group.addWidget(self.system_init_check)

        layout.addLayout(condition_group)

        # 添加一点空间
        layout.addSpacing(10)

        # 计划执行前提醒窗口设置
        schedule_prompt_label = QLabel("计划执行前显示提醒窗口(可通过选择进行执行)")
        layout.addWidget(schedule_prompt_label)

        # 自动关闭设置
        schedule_autoclose = QHBoxLayout()
        self.schedule_autoclose_check = QCheckBox("自动关闭")
        schedule_autoclose.addWidget(self.schedule_autoclose_check)

        self.schedule_autoclose_time = QSpinBox()
        self.schedule_autoclose_time.setRange(0, 100)
        self.schedule_autoclose_time.setValue(30)
        self.schedule_autoclose_time.setSuffix(" 秒后")
        schedule_autoclose.addWidget(self.schedule_autoclose_time)

        schedule_autoclose.addStretch()
        layout.addLayout(schedule_autoclose)

        # 添加一点空间
        layout.addSpacing(10)

        # 热键执行前提醒窗口设置
        hotkey_prompt_label = QLabel("热键执行前显示提醒窗口")
        layout.addWidget(hotkey_prompt_label)

        # 自动关闭设置
        hotkey_autoclose = QHBoxLayout()
        self.hotkey_autoclose_check = QCheckBox("自动关闭")
        hotkey_autoclose.addWidget(self.hotkey_autoclose_check)

        self.hotkey_autoclose_time = QSpinBox()
        self.hotkey_autoclose_time.setRange(0, 100)
        self.hotkey_autoclose_time.setValue(5)
        self.hotkey_autoclose_time.setSuffix(" 秒后")
        hotkey_autoclose.addWidget(self.hotkey_autoclose_time)

        hotkey_autoclose.addStretch()
        layout.addLayout(hotkey_autoclose)

        # 添加一点空间
        layout.addSpacing(10)

        # 软件启动和恢复选项
        self.startup_execute_check = QCheckBox("软件启动时执行错过的任务")
        layout.addWidget(self.startup_execute_check)

        self.resume_execute_check = QCheckBox("从待机(睡眠或休眠)中恢复后执行错过的任务")
        layout.addWidget(self.resume_execute_check)

        # 添加一点空间
        layout.addSpacing(10)

        # 限制触发次数
        limit_count_layout = QHBoxLayout()
        limit_count_layout.addWidget(QLabel("限制计划的触发次数"))

        self.limit_count_spin = QSpinBox()
        self.limit_count_spin.setRange(0, 9999)
        self.limit_count_spin.setValue(1)
        limit_count_layout.addWidget(self.limit_count_spin)

        limit_count_layout.addStretch()
        layout.addLayout(limit_count_layout)

        # 时间范围设置
        time_range_layout = QHBoxLayout()
        time_range_layout.addWidget(QLabel("只在时间段内执行"))

        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems(["无限制", "上午", "下午", "工作时间", "自定义"])
        time_range_layout.addWidget(self.time_range_combo)

        # 设置按钮
        time_range_settings = QPushButton()
        time_range_settings.setIcon(QIcon(":/icons/settings.png"))
        time_range_settings.setFixedSize(24, 24)
        time_range_settings.setStyleSheet("background-color: transparent; border: none;")
        time_range_layout.addWidget(time_range_settings)

        time_range_layout.addStretch()

        # 帮助按钮
        help_button = QPushButton("?")
        help_button.setFixedSize(24, 24)
        help_button.setStyleSheet("background-color: transparent; border: none;")
        time_range_layout.addWidget(help_button)

        layout.addLayout(time_range_layout)

        # 自动删除选项
        self.auto_delete_check = QCheckBox("计划执行后自动删除本任务")
        layout.addWidget(self.auto_delete_check)

        # 添加底部空白
        layout.addStretch()

        return tab

    def load_task_config(self, task_config):
        """加载任务配置到界面控件"""
        # 设置基本信息
        self.task_name_edit.setText(task_config.get("name", ""))

        # 设置任务类型
        task_type = task_config.get("type", "")
        task_subtype = task_config.get("subtype", "")

        if task_type and task_subtype:
            # 如果数据中有明确的一级和二级分类
            self.current_task_type["parent"] = task_type
            self.current_task_type["child"] = task_subtype
            # 设置显示
            if hasattr(self, "task_type_edit"):
                self.task_type_edit.setText(f"{task_type} > {task_subtype}")
                logger.debug(f"已设置任务类型: {task_type} > {task_subtype}")
        elif task_type:
            # 如果只有一个类型字段，尝试找到匹配的二级分类
            found = False
            for parent, children in self.task_types.items():
                if task_type in children:
                    self.current_task_type["parent"] = parent
                    self.current_task_type["child"] = task_type
                    if hasattr(self, "task_type_edit"):
                        self.task_type_edit.setText(f"{parent} > {task_type}")
                        logger.debug(f"已设置任务类型: {parent} > {task_type}")
                    found = True
                    break

            if not found:
                # 如果没有找到匹配，则仅设置父类型
                self.current_task_type["parent"] = task_type
                self.current_task_type["child"] = ""
                if hasattr(self, "task_type_edit"):
                    self.task_type_edit.setText(task_type)
                    logger.debug(f"已设置任务类型(仅父类型): {task_type}")
                logger.warning(f"未能找到任务类型的匹配: {task_type}")

        # 设置任务分类
        task_category = task_config.get("category", "")
        if task_category:
            index = self.task_category_combo.findText(task_category)
            if index >= 0:
                self.task_category_combo.setCurrentIndex(index)
            else:
                # 如果分类不存在，添加它
                self.task_category_combo.addItem(task_category)
                self.task_category_combo.setCurrentText(task_category)
        else:
            # 设置为"无"
            index = self.task_category_combo.findText("无")
            if index >= 0:
                self.task_category_combo.setCurrentIndex(index)

        # 加载自动关闭设置
        if hasattr(self, "auto_close_check"):
            auto_close = task_config.get("auto_close", False)
            self.auto_close_check.setChecked(auto_close)

            if hasattr(self, "auto_close_spin"):
                auto_close_minutes = task_config.get("auto_close_minutes", 0)
                self.auto_close_spin.setValue(auto_close_minutes)
                self.auto_close_spin.setEnabled(auto_close)  # 根据auto_close状态设置

            if hasattr(self, "auto_close_seconds_spin"):
                auto_close_seconds = task_config.get("auto_close_seconds", 30)
                self.auto_close_seconds_spin.setValue(auto_close_seconds)
                self.auto_close_seconds_spin.setEnabled(auto_close)  # 根据auto_close状态设置

            logger.debug(
                f"加载自动关闭设置: 启用={auto_close}, "
                + f"分钟={task_config.get('auto_close_minutes', 0)}, "
                + f"秒={task_config.get('auto_close_seconds', 30)}"
            )

        # 加载稍后提醒设置
        if hasattr(self, "remind_later_check"):
            remind_later = task_config.get("remind_later", False)
            self.remind_later_check.setChecked(remind_later)

            if hasattr(self, "remind_later_spin"):
                remind_later_minutes = task_config.get("remind_later_minutes", 5)
                self.remind_later_spin.setValue(remind_later_minutes)
                self.remind_later_spin.setEnabled(remind_later)  # 根据remind_later状态设置

            if hasattr(self, "remind_later_seconds_spin"):
                remind_later_seconds = task_config.get("remind_later_seconds", 0)
                self.remind_later_seconds_spin.setValue(remind_later_seconds)
                self.remind_later_seconds_spin.setEnabled(remind_later)  # 根据remind_later状态设置

            if hasattr(self, "remind_later_count_spin"):
                remind_later_count = task_config.get("remind_later_count", 10)
                self.remind_later_count_spin.setValue(remind_later_count)
                self.remind_later_count_spin.setEnabled(remind_later)  # 根据remind_later状态设置

            logger.debug(
                f"加载稍后提醒设置: 启用={remind_later}, "
                + f"分钟={task_config.get('remind_later_minutes', 5)}, "
                + f"秒={task_config.get('remind_later_seconds', 0)}, "
                + f"次数={task_config.get('remind_later_count', 10)}"
            )

        # 加载是否启用计划
        self.enable_schedule_check.setChecked(task_config.get("enabled", True))

        # 设置热键
        hotkey_config = task_config.get("hotkey_config", {})
        if hotkey_config:
            self.enable_hotkey_check.setChecked(True)

            if hotkey_config.get("type") == "keyboard":
                self.use_keyboard_radio.setChecked(True)

                # 设置修饰键
                modifiers = hotkey_config.get("modifiers", [])
                self.ctrl_check.setChecked("Ctrl" in modifiers)
                self.alt_check.setChecked("Alt" in modifiers)
                self.shift_check.setChecked("Shift" in modifiers)
                self.win_check.setChecked("Win" in modifiers)

                key = hotkey_config.get("key", "")
                index = self.key_combo.findText(key)
                if index >= 0:
                    self.key_combo.setCurrentIndex(index)

            elif hotkey_config.get("type") == "mouse":
                self.use_mouse_radio.setChecked(True)

                # 设置鼠标按键
                buttons = hotkey_config.get("buttons", [])
                self.left_button_check.setChecked("左键" in buttons)
                self.middle_button_check.setChecked("中键" in buttons)
                self.right_button_check.setChecked("右键" in buttons)

                # 设置滚轮
                wheel = hotkey_config.get("wheel", "")
                if wheel:
                    self.wheel_check.setChecked(True)
                    index = self.wheel_direction.findText(wheel)
                    if index >= 0:
                        self.wheel_direction.setCurrentIndex(index)
        else:
            self.enable_hotkey_check.setChecked(False)

        # 获取内容 - 按优先级从不同位置获取
        content = ""

        # 1. 首先尝试从actions中获取
        actions = task_config.get("actions", [])
        if actions and len(actions) > 0 and "params" in actions[0]:
            content = actions[0]["params"].get("content", "")
            logger.debug(f"从actions[0].params.content获取内容: {content}")

        # 2. 如果没有，尝试从alert_message获取
        if not content:
            content = task_config.get("alert_message", "")
            if content:
                logger.debug(f"从alert_message获取内容: {content}")

        # 3. 如果还没有，尝试从tts配置中获取
        if not content and "tts" in task_config and isinstance(task_config["tts"], dict):
            content = task_config["tts"].get("text", "")
            if content:
                logger.debug(f"从tts.text获取内容: {content}")

        # 4. 如果还没有，尝试从旧的tts_text字段获取
        if not content:
            content = task_config.get("tts_text", "")
            if content:
                logger.debug(f"从tts_text获取内容: {content}")

        # 记录最终使用的内容
        logger.info(f"任务对话框加载内容: '{content}'")

        # 定义content_text变量，供后续TTS和动作配置使用
        content_text = content

        # 设置内容到编辑框
        if hasattr(self, "content_edit") and isinstance(self.content_edit, QTextEdit):
            self.content_edit.setText(content)

        # 设置TTS相关选项
        if hasattr(self, "tts_radio") and hasattr(self, "play_sound_radio"):
            # 检查是否启用TTS
            tts_enabled = False

            # 首先检查新的结构化配置
            if "tts" in task_config and isinstance(task_config["tts"], dict):
                tts_enabled = task_config["tts"].get("enabled", False)

            # 如果没有，检查旧的扁平结构
            if not tts_enabled:
                tts_enabled = task_config.get("enable_tts", False)

            # 设置单选按钮状态
            if tts_enabled:
                self.tts_radio.setChecked(True)
            else:
                self.play_sound_radio.setChecked(True)

            # 切换相应的控件可见性
            self.toggle_sound_tts_widgets(None)

            # 设置TTS语音和速率
            if hasattr(self, "voice_combo") and hasattr(self, "tts_rate_slider"):
                # 获取语音ID
                voice_id = None
                if "tts" in task_config and isinstance(task_config["tts"], dict):
                    voice_id = task_config["tts"].get("voice_id")
                if not voice_id:
                    voice_id = task_config.get("tts_voice_id")

                # 设置语音
                if voice_id and self.voice_combo.count() > 0:
                    for i in range(self.voice_combo.count()):
                        if self.voice_combo.itemData(i) == voice_id:
                            self.voice_combo.setCurrentIndex(i)
                            break

                # 获取语速
                rate = 175  # 默认值
                if "tts" in task_config and isinstance(task_config["tts"], dict):
                    rate = task_config["tts"].get("rate", 175)
                else:
                    rate = task_config.get("tts_rate", 175)

                # 设置语速
                self.tts_rate_slider.setValue(rate)

            # 加载声音设置
            if hasattr(self, "sound_combo") and hasattr(self, "repeat_spin"):
                # 设置声音文件
                sound_file = task_config.get("sound_file", None)
                if sound_file:
                    # 查找声音文件并设置
                    for i in range(self.sound_combo.count()):
                        if self.sound_combo.itemData(i) == sound_file:
                            self.sound_combo.setCurrentIndex(i)
                            logger.debug(f"设置声音文件: {sound_file}")
                            break

                # 设置播放时间
                sound_play_time = task_config.get("sound_play_time", 10)
                self.repeat_spin.setValue(sound_play_time)
                logger.debug(f"设置声音播放时间: {sound_play_time}")

        # 加载背景色和字体颜色设置
        if hasattr(self, "bg_color") and hasattr(self, "font_color"):

            # 从配置中读取颜色
            bg_color_str = task_config.get("bg_color", "#8B4513")  # 默认棕色
            font_color_str = task_config.get("font_color", "#FF0000")  # 默认红色

            try:
                # 创建QColor对象
                bg_color = QColor(bg_color_str)
                font_color = QColor(font_color_str)

                if bg_color.isValid() and font_color.isValid():
                    # 更新对象属性
                    self.bg_color = bg_color
                    self.font_color = font_color

                    logger.debug(f"加载背景色: {bg_color_str}, 字体颜色: {font_color_str}")

                    # 更新颜色按钮UI
                    bg_button = self.findChild(QPushButton, "bgColorButton")
                    font_button = self.findChild(QPushButton, "fontColorButton")

                    if bg_button:
                        bg_button.setStyleSheet(
                            f"""
                            QPushButton {{
                                background-color: {bg_color.name()};
                                color: {font_color.name()};
                                border: 1px solid {bg_color.darker(120).name()};
                                border-radius: 3px;
                                padding: 4px 6px;
                                text-align: center;
                            }}
                        """
                        )

                    if font_button:
                        font_button.setStyleSheet(
                            f"""
                            QPushButton {{
                                background-color: white;
                                color: {font_color.name()};
                                border: 1px solid #990000;
                                border-radius: 3px;
                                padding: 4px 6px;
                                text-align: center;
                            }}
                        """
                        )
            except Exception as e:
                logger.error(f"加载颜色设置时出错: {e}")

        # 加载其他显示设置
        if hasattr(self, "center_display_check"):
            self.center_display_check.setChecked(task_config.get("is_centered", True))

        if hasattr(self, "top_display_check"):
            self.top_display_check.setChecked(task_config.get("is_top_most", True))

        if hasattr(self, "transparency_slider") and "transparency" in task_config:
            self.transparency_slider.setValue(task_config.get("transparency", 0))
            if hasattr(self, "transparency_value_label"):
                self.transparency_value_label.setText(str(task_config.get("transparency", 0)))

        # 根据任务类型调整UI显示
        if self.current_task_type["parent"] and self.current_task_type["child"]:
            self.adjust_ui_by_task_type(
                self.current_task_type["parent"], self.current_task_type["child"]
            )

        # 设置TTS配置
        tts_config = {}
        if (
            hasattr(self, "tts_radio")
            and hasattr(self, "voice_combo")
            and hasattr(self, "tts_rate_slider")
        ):
            tts_enabled = self.tts_radio.isChecked()
            voice_id = (
                self.voice_combo.currentData() if self.voice_combo.currentIndex() >= 0 else None
            )
            rate = self.tts_rate_slider.value()

            tts_config = {
                "enabled": tts_enabled,
                "text": content_text,
                "voice_id": voice_id,
                "rate": rate,
            }

            # 同时设置旧格式的TTS字段，以保持兼容性
            task_config["enable_tts"] = tts_enabled
            task_config["tts_text"] = content_text
            task_config["tts_voice_id"] = voice_id
            task_config["tts_rate"] = rate

        task_config["tts"] = tts_config

        # 保存声音设置
        if hasattr(self, "play_sound_radio") and hasattr(self, "sound_combo"):
            sound_enabled = self.play_sound_radio.isChecked()
            sound_file = self.sound_combo.currentData()  # 获取关联的文件名
            sound_display = self.sound_combo.currentText()

            # 保存声音配置
            task_config["sound_enabled"] = sound_enabled
            task_config["sound_file"] = sound_file
            task_config["sound_display"] = sound_display

            # 保存播放时间设置
            if hasattr(self, "repeat_spin"):
                task_config["sound_play_time"] = self.repeat_spin.value()

            logger.debug(
                f"保存声音设置: 启用={sound_enabled}, "
                + f"文件={sound_file}, "
                + f"显示名={sound_display}, "
                + f"播放时间={task_config.get('sound_play_time', 10)}"
            )

        # 保存背景色和字体颜色
        if hasattr(self, "bg_color") and hasattr(self, "font_color"):
            # 将QColor对象转换为十六进制色值字符串
            bg_color_str = (
                self.bg_color.name() if self.bg_color and self.bg_color.isValid() else "#8B4513"
            )  # 默认棕色
            font_color_str = (
                self.font_color.name()
                if self.font_color and self.font_color.isValid()
                else "#FF0000"
            )  # 默认红色

            logger.debug(f"保存背景色: {bg_color_str}, 字体颜色: {font_color_str}")

            # 保存到任务配置
            task_config["bg_color"] = bg_color_str
            task_config["font_color"] = font_color_str

            # 保存其他显示设置
            task_config["is_centered"] = (
                self.center_display_check.isChecked()
                if hasattr(self, "center_display_check")
                else True
            )
            task_config["is_top_most"] = (
                self.top_display_check.isChecked() if hasattr(self, "top_display_check") else True
            )

            # 保存透明度
            if hasattr(self, "transparency_slider"):
                transparency = self.transparency_slider.value()
                task_config["transparency"] = transparency

        # 保存自动关闭设置
        if hasattr(self, "auto_close_check"):
            task_config["auto_close"] = self.auto_close_check.isChecked()

            # 保存自动关闭时间
            if hasattr(self, "auto_close_spin"):
                task_config["auto_close_minutes"] = self.auto_close_spin.value()

            if hasattr(self, "auto_close_seconds_spin"):
                task_config["auto_close_seconds"] = self.auto_close_seconds_spin.value()

            logger.debug(
                f"保存自动关闭设置: 启用={task_config['auto_close']}, "
                + f"分钟={task_config.get('auto_close_minutes', 0)}, "
                + f"秒={task_config.get('auto_close_seconds', 0)}"
            )

        # 保存稍后提醒设置
        if hasattr(self, "remind_later_check"):
            task_config["remind_later"] = self.remind_later_check.isChecked()

            # 保存稍后提醒时间
            if hasattr(self, "remind_later_spin"):
                task_config["remind_later_minutes"] = self.remind_later_spin.value()

            if hasattr(self, "remind_later_seconds_spin"):
                task_config["remind_later_seconds"] = self.remind_later_seconds_spin.value()

            # 保存稍后提醒次数
            if hasattr(self, "remind_later_count_spin"):
                task_config["remind_later_count"] = self.remind_later_count_spin.value()

            logger.debug(
                f"保存稍后提醒设置: 启用={task_config.get('remind_later', False)}, "
                + f"分钟={task_config.get('remind_later_minutes', 0)}, "
                + f"秒={task_config.get('remind_later_seconds', 0)}, "
                + f"次数={task_config.get('remind_later_count', 0)}"
            )

        # 动作设置 - 根据任务类型设置动作
        actions = task_config.get("actions", [])
        if not actions:
            # 创建一个默认动作
            action = {
                "id": str(uuid.uuid4()),
                "type": self.current_task_type["child"] or "notification",
                "name": self.current_task_type["child"] or "提醒",
                "params": {"content": content_text or "新任务"},
            }
            actions.append(action)
            logger.debug(f"创建新动作，内容: {content_text}")
        # 更新现有动作的内容
        elif actions and len(actions) > 0:
            if "params" not in actions[0]:
                actions[0]["params"] = {}

            # 确保内容完整保存并且是字符串类型
            actions[0]["params"]["content"] = content_text
            logger.debug(f"更新现有动作内容: {content_text}")

        task_config["actions"] = actions

        return task_config

    def accept(self):
        """确认按钮点击处理"""
        # 验证输入
        if not self.task_name_edit.text():
            # 显示错误消息
            QMessageBox.warning(self, "输入错误", "请输入任务名称")
            return

        # 更新任务配置
        self.task_config = self.get_task_config()

        # 记录保存的任务配置信息
        logger.info(f"保存任务配置: {self.task_config.get('name')}")

        # 记录内容字段
        content_text = ""
        if hasattr(self, "content_edit"):
            content_text = self.content_edit.toPlainText()
        logger.info(f"保存的内容: '{content_text}'")

        # 记录actions内容
        actions = self.task_config.get("actions", [])
        if actions and len(actions) > 0 and "params" in actions[0]:
            action_content = actions[0]["params"].get("content", "")
            logger.info(f"保存的actions[0].params.content: '{action_content}'")

        # 调用父类方法完成接受
        super().accept()

    def _play_selected_sound(self):
        """使用 pygame.mixer 播放选中的声音文件"""
        selected_sound_filename = self.sound_combo.currentData()  # 获取关联的文件名
        display_name = self.sound_combo.currentText()

        if selected_sound_filename is None or display_name == "无":
            logger.debug("没有选择声音或选择了'无'，不播放。")
            return

        if not TaskDialog._mixer_initialized:
            logger.error("Pygame mixer 未初始化，无法播放声音。")
            QMessageBox.critical(self, "声音错误", "音频播放器未成功初始化，无法试听。")
            return

        try:
            # 尝试在两个可能的位置查找声音文件
            sound_dirs = [
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "resources", "sounds"),
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "sounds"),
            ]

            sound_path = None
            for sound_dir in sound_dirs:
                potential_path = os.path.join(sound_dir, selected_sound_filename)
                if os.path.exists(potential_path):
                    sound_path = potential_path
                    break

            if not sound_path:
                logger.warning(f"未找到声音文件: {selected_sound_filename}")
                QMessageBox.warning(
                    self,
                    "提示",
                    f"未找到声音文件：\n{selected_sound_filename}\n\n请确认文件存在于声音目录",
                )
                return

            logger.info(f"尝试使用 pygame 播放声音文件: {sound_path}")

            # 停止当前可能正在播放的音乐
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
                pygame.mixer.music.unload()  # 卸载之前的，避免干扰
                logger.debug("停止了之前正在播放的音乐。")
                # 短暂等待确保停止
                pygame.time.wait(50)

            # 加载新音乐
            pygame.mixer.music.load(sound_path)
            pygame.mixer.music.play()
            logger.debug(f"已开始播放: {selected_sound_filename}")

        except Exception as e:  # 处理所有pygame和其他可能的错误
            logger.error(
                f"播放声音时出错 ({selected_sound_filename}): {e}",
                exc_info=True,
            )
            QMessageBox.critical(
                self,
                "播放错误",
                f"播放声音文件时遇到错误：\n{selected_sound_filename}\n错误: {str(e)}",
            )

    def load_sound_files(self):
        """加载声音文件到声音下拉列表 (递归搜索子目录)"""
        # 清空当前的声音列表
        self.sound_combo.clear()

        # 定义可能的声音目录路径
        base_dirs = [
            os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "resources", "sounds"
            ),
            os.path.join("fay-client", "resources", "sounds"),
            os.path.join(os.getcwd(), "resources", "sounds"),
            os.path.join(os.getcwd(), "fay-client", "resources", "sounds"),
        ]

        logger.info(f"开始加载声音文件，尝试以下目录: {base_dirs}")

        found_sounds = False

        # 遍历所有可能的目录
        for base_dir in base_dirs:
            # 检查主目录
            if os.path.isdir(base_dir):
                logger.info(f"检查声音目录: {base_dir}")
                # 遍历主目录中的文件
                for filename in os.listdir(base_dir):
                    if filename.lower().endswith((".wav", ".mp3")):
                        full_path = os.path.join(base_dir, filename)
                        file_name_only = os.path.splitext(filename)[0]
                        self.sound_combo.addItem(file_name_only, userData=full_path)
                        found_sounds = True
                        logger.info(f"找到声音文件: {full_path}")

                # 检查prompt子目录
                prompt_dir = os.path.join(base_dir, "prompt")
                if os.path.isdir(prompt_dir):
                    logger.info(f"检查prompt子目录: {prompt_dir}")
                    for filename in os.listdir(prompt_dir):
                        if filename.lower().endswith((".wav", ".mp3")):
                            full_path = os.path.join(prompt_dir, filename)
                            file_name_only = os.path.splitext(filename)[0]
                            self.sound_combo.addItem(file_name_only, userData=full_path)
                            found_sounds = True
                            logger.info(f"找到声音文件: {full_path}")

        # 如果没有找到任何声音文件
        if not found_sounds:
            logger.warning("未找到任何声音文件")
            self.sound_combo.addItem("无可用声音")
            self.sound_combo.setEnabled(False)
        else:
            self.sound_combo.setEnabled(True)
            logger.info(f"成功加载 {self.sound_combo.count()} 个声音文件")

            # 设置第一个声音为默认选择
            if self.sound_combo.count() > 0:
                self.sound_combo.setCurrentIndex(0)

        # 初始化pygame mixer (如果尚未初始化)
        if not TaskDialog._mixer_initialized:
            try:
                pygame.mixer.init()
                TaskDialog._mixer_initialized = True
                logger.info("Pygame mixer 初始化成功。")
            except Exception as e:
                logger.error(f"初始化Pygame mixer时出错: {e}", exc_info=True)
                TaskDialog._mixer_initialized = False

    def show_bg_color_dialog(self):
        """显示背景颜色选择对话框"""
        color = show_color_dialog(self, self.bg_color, True)
        if color and color.isValid():
            self.bg_color = color
            # 只更新背景颜色按钮
            bg_button = self.findChild(QPushButton, "bgColorButton")
            if bg_button:
                bg_color_str = self.bg_color.name()
                font_color_str = (
                    self.font_color.name()
                    if self.font_color and self.font_color.isValid()
                    else "#FFFFFF"
                )
                bg_button.setStyleSheet(
                    f"""
                    QPushButton {{
                        background-color: {bg_color_str};
                        color: {font_color_str};
                        border: 1px solid #5C2E0B;
                        border-radius: 3px;
                        padding: 4px 6px;
                        text-align: center;
                    }}
                """
                )
            return True
        return False

    def toggle_sound_tts_widgets(self, _=None):
        """根据选择的单选按钮切换声音/TTS控件的可见性

        Args:
            _: 未使用的参数，用于兼容Qt信号槽连接
        """
        is_tts = self.tts_radio.isChecked()
        self.sound_widgets_container.setVisible(not is_tts)
        self.tts_widgets_container.setVisible(is_tts)

    def on_voice_changed(self, index):
        """当用户选择不同的TTS语音时调用 (根据 作风哥 方案)"""
        if not self.tts_engine:
            return

        new_selected_voice_id = self.voice_combo.itemData(index)
        if new_selected_voice_id is None:
            logger.warning("on_voice_changed: 无效的语音 ID。")
            return

        logger.info(
            f"用户切换语音下拉框至索引 {index}, "
            f"语音ID: {new_selected_voice_id}, "
            f"名称: {self.voice_combo.currentText()}"
        )

        if self.is_tts_busy:
            # --- Scenario: Engine switched WHILE TTS is busy (playing) ---
            if new_selected_voice_id != self.current_playback_voice_id:
                logger.debug("播放时切换到不同引擎。当前播放将继续，按钮变为播放状态。")
                # Requirement: "播放继续（使用旧引擎），但按钮变为"播放"图标"
                self.pending_new_engine_play = True  # Set flag
            else:
                # Switched back to the currently playing engine, or no real change
                logger.debug("播放时切换回当前引擎或无实际变化。")
                self.pending_new_engine_play = False
        else:
            # --- Scenario: Engine switched while TTS is idle ---
            # Button remains "Play", next play will use the new engine.
            logger.debug("空闲时切换引擎。")
            self.pending_new_engine_play = False

        self._update_play_button_ui()  # 更新按钮状态

    def on_tts_rate_changed(self, value):
        """当语速滑块值改变时调用"""
        self.tts_rate_label.setText(str(value))
        if self.tts_engine:
            try:
                self.tts_engine.setProperty("rate", value)
                logger.debug(f"TTS语速已更改为: {value}")
            except Exception as e:
                logger.error(f"设置TTS语速失败: {e}")

    # --- TTS 控制逻辑 (根据 作风哥 方案) ---

    def _update_play_button_ui(self):
        """根据当前状态更新播放/停止按钮的图标和提示。"""
        if self.is_tts_busy:
            self.test_voice_button.setIcon(QApplication.style().standardIcon(QStyle.SP_MediaStop))
            self.test_voice_button.setToolTip("停止试听")
        elif self.pending_new_engine_play:
            self.test_voice_button.setIcon(QApplication.style().standardIcon(QStyle.SP_MediaPlay))
            self.test_voice_button.setToolTip("使用新选引擎试听")  # 提示用户下次播放会用新引擎
        else:
            self.test_voice_button.setIcon(QApplication.style().standardIcon(QStyle.SP_MediaPlay))
            self.test_voice_button.setToolTip("试听选定语音")

    def _engine_on_started_utterance(self, name):
        """Pyttsx3引擎回调：确认语句已开始（主要用于调试）。"""
        logger.debug(f"引擎回调 started-utterance: {name}")
        # 主状态由 worker 的 playback_started 信号管理
        pass

    def _engine_on_finished_utterance(self, name, completed):
        """Pyttsx3引擎回调：确认语句已完成（主要用于调试）。"""
        logger.debug(f"引擎回调 finished-utterance: {name}, Completed: {completed}")
        # 主状态由 worker 的 playback_finished 信号管理
        pass

    def toggle_playback(self):
        """处理播放/停止按钮点击。"""
        text_to_speak = self.content_edit.toPlainText().strip()
        if not text_to_speak:
            QMessageBox.information(self, "提示", "请输入要试听的内容。")
            return

        if not self.tts_engine or not self.available_voices:
            logger.warning("TTS引擎未初始化或无可用声音，无法操作。")
            QMessageBox.warning(self, "提示", "语音引擎未初始化或无可用声音。")
            return

        if self.is_tts_busy:
            # --- 如果正在播放，则停止 ---
            logger.info("用户点击停止按钮。")

            # 立即将状态标记为非忙碌，防止多次点击
            self.is_tts_busy = False
            self._update_play_button_ui()  # 立即更新UI为播放状态

            try:
                # 防止线程和引擎状态不同步
                if self.tts_thread and self.tts_thread.isRunning():
                    logger.debug("停止前检测到活动线程，尝试停止线程...")
                    # 先停止引擎，这会触发runAndWait()退出
                    self.tts_engine.stop()
                    logger.info("已调用tts_engine.stop()，等待线程退出...")

                    # 设置一个短暂的超时等待线程退出
                    if not self.tts_thread.wait(300):
                        logger.warning("线程未在预期时间内退出，将强制重置状态")
                        # 如果超时，使用强制重置
                        self._force_reset_tts_state()
                    else:
                        logger.debug("线程已正常退出，清理引用")
                        self._clear_thread_references()
                else:
                    # 即使没有检测到活动线程，也尝试停止引擎
                    self.tts_engine.stop()
                    logger.info("已调用tts_engine.stop()，无活动线程")

                    # 确保清理全部状态
                    self._clear_thread_references()

                # 无论如何都安排延时重置，作为最后的保障措施
                QTimer.singleShot(500, self._force_reset_tts_state)

            except Exception as e:
                logger.error(f"尝试停止TTS时出错: {e}", exc_info=True)
                # 出错时立即强制重置
                self._force_reset_tts_state()
        else:
            # --- 如果未播放，则开始播放 ---
            logger.info("用户点击播放按钮。")

            # 获取要朗读的文本
            text_to_speak = self.content_edit.toPlainText().strip()
            if not text_to_speak:
                QMessageBox.information(self, "提示", "请输入要试听的内容。")
                return

            # 获取当前选择的语音ID和速率
            current_index = self.voice_combo.currentIndex()
            if current_index < 0:
                logger.warning("未选择有效的语音。")
                QMessageBox.warning(self, "提示", "请先选择一个发声引擎。")
                return

            current_rate = self.tts_rate_slider.value()
            voice_id = self.voice_combo.itemData(current_index)

            try:
                if self.tts_thread and self.tts_thread.isRunning():
                    logger.warning("之前的TTS线程似乎仍在运行。尝试等待其结束...")
                    self.tts_thread.quit()
                    if not self.tts_thread.wait(500):
                        logger.error("无法正常结束之前的TTS线程！")
                        # 强制重置状态
                        self._force_reset_tts_state()
                    else:
                        logger.debug("之前的TTS线程已结束。")

                # --- 创建并启动 Worker 和 Thread ---
                logger.debug("创建 TTSWorker 和 QThread...")
                self.is_tts_busy = True  # *设置状态为 True*
                self.pending_new_engine_play = False  # 清除待处理标记
                self.current_playback_voice_id = voice_id  # 记录本次播放使用的 voice id
                self._update_play_button_ui()  # *立即更新UI为停止状态*

                self.worker = TTSWorker(self.tts_engine, text_to_speak, voice_id, current_rate)
                self.tts_thread = QThread(self)  # 关联父对象，便于管理
                self.worker.moveToThread(self.tts_thread)

                # 连接信号槽
                self.worker.playback_started.connect(self.on_playback_started)
                self.worker.playback_finished.connect(self.on_playback_finished)
                self.worker.error_occurred.connect(self.on_playback_error)
                self.tts_thread.started.connect(self.worker.run)
                # 确保线程退出
                self.worker.playback_finished.connect(self.tts_thread.quit)
                # 清理资源
                self.tts_thread.finished.connect(self.worker.deleteLater)
                self.tts_thread.finished.connect(self.tts_thread.deleteLater)
                self.tts_thread.finished.connect(self._clear_thread_references)

                logger.debug("启动新的TTS播放线程...")
                self.tts_thread.start()

            except Exception as e:
                logger.error(f"启动TTS朗读失败: {e}", exc_info=True)
                # 如果启动过程出错，需要重置状态
                self._force_reset_tts_state()
                QMessageBox.critical(self, "启动错误", f"启动TTS朗读时出错: {e}")

    # --- 新增槽函数 ---

    def on_playback_started(self):
        """工作线程发出的 playback_started 信号的槽函数。"""
        logger.info("槽函数 on_playback_started: 播放已开始。")
        # is_tts_busy 已经在 toggle_playback 中设置为 True
        # _update_play_button_ui 也已调用
        # 这里主要是为了日志或未来可能的扩展
        pass

    def on_playback_finished(self):
        """工作线程发出的 playback_finished 信号的槽函数。"""
        logger.info("槽函数 on_playback_finished: 播放已完成或停止。")

        # 检查线程状态，避免重复处理
        if not self.is_tts_busy:
            logger.debug("TTS已处于空闲状态，忽略该信号。")
            return

        # 重置状态
        self.is_tts_busy = False

        # 检查是否有挂起的引擎切换
        if self.pending_new_engine_play:
            # 如果之前播放时切换了引擎，现在旧引擎播放完毕，
            # UI 保持"播放（新引擎）"状态，等待用户点击。
            logger.debug("播放结束，但有待处理的新引擎播放请求。")
            # self.pending_new_engine_play 保持 True
        else:
            self.pending_new_engine_play = False  # 确保清除标记

        # 立即更新UI为播放状态
        self._update_play_button_ui()

        # 确保引擎不再运行（双重保险）
        try:
            if self.tts_engine:
                self.tts_engine.stop()
        except Exception as e:
            logger.debug(f"完成处理时二次停止引擎出错(可忽略): {e}")

        # 线程和worker的清理由 self.tts_thread.finished 信号处理
        # 但为防止信号丢失，再次请求线程退出
        if self.tts_thread and self.tts_thread.isRunning():
            try:
                logger.debug("完成时请求线程退出")
                self.tts_thread.quit()
                # 尝试短时间等待线程结束
                if not self.tts_thread.wait(100):
                    logger.debug("线程未立即结束，将继续异步清理")
            except Exception as e:
                logger.debug(f"完成处理时请求线程退出出错(可忽略): {e}")

        # 最后设置一个延时，确保完全清理资源
        QTimer.singleShot(300, self._clear_thread_references)

    def _clear_thread_references(self):
        """清理线程和worker的引用。"""
        logger.debug("清理 TTS 线程和 Worker 引用。")
        self.tts_thread = None
        self.worker = None

    def open_task_type_search_dialog(self):
        """打开任务类型搜索对话框"""
        try:
            logger.info("打开任务类型搜索对话框")

            # 导入搜索对话框类
            from .task_type_search_dialog import TaskTypeSearchDialog

            # 将任务类型转换为搜索对话框需要的格式
            task_types_list = []
            for parent_type, child_types in self.task_types.items():
                for child_type in child_types:
                    task_types_list.append((parent_type, child_type))

            # 创建搜索对话框
            search_dialog = TaskTypeSearchDialog(self, task_types_list)

            # 连接类型选择信号
            search_dialog.typeSelected.connect(self.on_task_type_selected)

            # 显示对话框
            search_dialog.exec()

        except Exception as e:
            logger.error(f"打开任务类型搜索对话框时出错: {e}", exc_info=True)

    def _force_reset_tts_state(self):
        """强制重置TTS状态，用于处理异常情况"""
        logger.debug("强制重置TTS状态。")
        # 重置所有相关标志
        self.is_tts_busy = False
        self.pending_new_engine_play = False

        # 确保引擎不再运行（双重保险）
        try:
            if self.tts_engine:
                self.tts_engine.stop()
                logger.debug("在_force_reset_tts_state中停止引擎")
        except Exception as e:
            logger.error(f"_force_reset_tts_state中停止引擎出错: {e}")

        # 清理线程和工作器
        if self.tts_thread and self.tts_thread.isRunning():
            try:
                logger.debug("在_force_reset_tts_state中退出线程")
                self.tts_thread.quit()
                # 等待一小段时间，但不过长阻塞UI
                if not self.tts_thread.wait(200):
                    logger.warning("线程未能在时限内退出，将继续执行")
            except Exception as e:
                logger.error(f"强制重置状态时退出线程出错: {e}")

        # 完全销毁并重新初始化引擎（核心修复）
        if self.tts_engine:
            try:
                # 保存当前引擎配置
                current_rate = self.tts_engine.getProperty("rate")
                current_voice = None
                if self.voice_combo.currentIndex() >= 0:
                    current_voice = self.voice_combo.itemData(self.voice_combo.currentIndex())

                # 完全销毁引擎资源
                logger.debug("完全销毁并创建新的TTS引擎实例")
                self.tts_engine = None

                # 强制执行一些Python垃圾回收，帮助释放资源
                import gc

                gc.collect()

                # 重新初始化引擎
                time.sleep(0.1)  # 短暂延迟，确保资源释放
                self.tts_engine = pyttsx3.init()

                if not self.tts_engine:
                    logger.error("TTS引擎重新初始化失败，返回None")
                    # 更新UI状态
                    self._update_play_button_ui()
                    # 清除引用
                    self._clear_thread_references()
                    return

                # 恢复配置
                if current_voice:
                    try:
                        self.tts_engine.setProperty("voice", current_voice)
                    except Exception as voice_e:
                        logger.error(f"恢复语音设置出错: {voice_e}")

                try:
                    self.tts_engine.setProperty("rate", current_rate)
                except Exception as rate_e:
                    logger.error(f"恢复语速设置出错: {rate_e}")

                # 重新连接回调
                try:
                    self.tts_engine.connect("started-utterance", self._engine_on_started_utterance)
                    self.tts_engine.connect(
                        "finished-utterance", self._engine_on_finished_utterance
                    )
                except Exception as connect_e:
                    logger.error(f"重新连接TTS回调出错: {connect_e}")

                logger.debug("TTS引擎已完全重新初始化")
            except Exception as e:
                logger.error(f"重新初始化TTS引擎出错: {e}")
                self.tts_engine = None  # 确保异常时引擎为None

        # 清除引用
        self._clear_thread_references()

        # 更新UI状态
        self._update_play_button_ui()

        logger.info("TTS状态已强制重置。")

    def on_playback_error(self, error_message):
        """工作线程发出的 error_occurred 信号的槽函数。"""
        logger.error(f"槽函数 on_playback_error: {error_message}")
        QMessageBox.warning(self, "TTS 播放错误", error_message)

        # 使用强制重置方法确保状态和UI被重置
        self._force_reset_tts_state()

    def closeEvent(self, event):
        """关闭对话框前的清理。"""
        logger.info("TaskDialog closeEvent triggered.")
        # 使用强制重置方法确保TTS资源正确清理
        if self.is_tts_busy:
            logger.debug("正在关闭对话框时停止TTS引擎...")
            self._force_reset_tts_state()
        super().closeEvent(event)  # Call the base class method

    def _on_auto_close_state_changed(self, state):
        """自动关闭复选框状态改变时的处理"""
        is_checked = state == Qt.Checked
        # 根据复选框状态启用或禁用时间输入框
        if hasattr(self, "auto_close_spin"):
            self.auto_close_spin.setEnabled(is_checked)
        if hasattr(self, "auto_close_seconds_spin"):
            self.auto_close_seconds_spin.setEnabled(is_checked)

        logger.debug(f"自动关闭状态改变: {is_checked}")

    def _on_later_remind_state_changed(self, state):
        """稍后提醒复选框状态改变时的处理"""
        # 修复状态判断问题 - 直接检查状态值
        is_checked = state == Qt.Checked
        logger.info(
            f"[DEBUG] 稍后提醒状态改变: state={state}, Qt.Checked={Qt.Checked}, is_checked={is_checked}"
        )

        # 额外的状态检查
        if state == 2:  # Qt.Checked 的值是 2
            is_checked = True
        elif state == 0:  # Qt.Unchecked 的值是 0
            is_checked = False
        else:
            is_checked = False

        logger.info(f"[DEBUG] 修正后的状态: is_checked={is_checked}")

        # 打印当前控件状态
        logger.info("[DEBUG] 当前控件状态检查:")
        logger.info(f"[DEBUG] - remind_later_spin存在: {hasattr(self, 'remind_later_spin')}")
        logger.info(
            f"[DEBUG] - remind_later_seconds_spin存在: {hasattr(self, 'remind_later_seconds_spin')}"
        )
        logger.info(
            f"[DEBUG] - remind_later_count_spin存在: {hasattr(self, 'remind_later_count_spin')}"
        )

        # 根据复选框状态启用或禁用时间输入框
        if hasattr(self, "remind_later_spin"):
            old_enabled = self.remind_later_spin.isEnabled()
            self.remind_later_spin.setEnabled(is_checked)
            new_enabled = self.remind_later_spin.isEnabled()
            logger.info(f"[DEBUG] remind_later_spin: {old_enabled} -> {new_enabled}")

        if hasattr(self, "remind_later_seconds_spin"):
            old_enabled = self.remind_later_seconds_spin.isEnabled()
            self.remind_later_seconds_spin.setEnabled(is_checked)
            new_enabled = self.remind_later_seconds_spin.isEnabled()
            logger.info(f"[DEBUG] remind_later_seconds_spin: {old_enabled} -> {new_enabled}")

        if hasattr(self, "remind_later_count_spin"):
            old_enabled = self.remind_later_count_spin.isEnabled()
            self.remind_later_count_spin.setEnabled(is_checked)
            new_enabled = self.remind_later_count_spin.isEnabled()
            logger.info(f"[DEBUG] remind_later_count_spin: {old_enabled} -> {new_enabled}")

        # 强制刷新UI - 多重刷新确保状态正确应用
        self.update()
        self.repaint()
        QApplication.processEvents()

        # 延迟再次刷新，确保所有控件状态都已更新
        QTimer.singleShot(10, lambda: self._delayed_ui_refresh(is_checked))

        logger.info(f"[DEBUG] 稍后提醒状态改变完成: {is_checked}, UI已刷新")

    def _delayed_ui_refresh(self, is_checked):
        """延迟UI刷新方法，确保控件状态正确应用"""
        logger.info(f"[DEBUG] 执行延迟UI刷新，is_checked={is_checked}")

        # 再次检查并设置控件状态
        if hasattr(self, "remind_later_spin"):
            current_enabled = self.remind_later_spin.isEnabled()
            if current_enabled != is_checked:
                logger.info(
                    f"[DEBUG] 延迟刷新：remind_later_spin状态不一致，重新设置: {current_enabled} -> {is_checked}"
                )
                self.remind_later_spin.setEnabled(is_checked)
                self.remind_later_spin.update()

        if hasattr(self, "remind_later_seconds_spin"):
            current_enabled = self.remind_later_seconds_spin.isEnabled()
            if current_enabled != is_checked:
                logger.info(
                    f"[DEBUG] 延迟刷新：remind_later_seconds_spin状态不一致，重新设置: {current_enabled} -> {is_checked}"
                )
                self.remind_later_seconds_spin.setEnabled(is_checked)
                self.remind_later_seconds_spin.update()

        if hasattr(self, "remind_later_count_spin"):
            current_enabled = self.remind_later_count_spin.isEnabled()
            if current_enabled != is_checked:
                logger.info(
                    f"[DEBUG] 延迟刷新：remind_later_count_spin状态不一致，重新设置: {current_enabled} -> {is_checked}"
                )
                self.remind_later_count_spin.setEnabled(is_checked)
                self.remind_later_count_spin.update()

        # 最终刷新
        self.update()
        QApplication.processEvents()
        logger.info("[DEBUG] 延迟UI刷新完成")

    def _test_manual_trigger_signal(self):
        """手动触发信号槽测试方法，用于验证修复效果"""
        logger.info("[DEBUG] 开始手动触发信号槽测试")

        if hasattr(self, "remind_later_check"):
            # 获取当前状态
            current_state = self.remind_later_check.isChecked()
            logger.info(f"[DEBUG] 当前复选框状态: {current_state}")

            # 手动触发信号槽
            if current_state:
                # 如果当前是选中状态，手动调用信号槽方法
                self._on_later_remind_state_changed(Qt.Checked)
            else:
                # 如果当前是未选中状态，手动调用信号槽方法
                self._on_later_remind_state_changed(Qt.Unchecked)

            logger.info("[DEBUG] 手动触发信号槽测试完成")
        else:
            logger.error("[DEBUG] remind_later_check控件不存在")

    def get_task_config(self):
        """从界面获取任务配置"""
        task_config = self.task_config.copy() if self.task_config else {}

        # 基本设置
        task_config["name"] = self.task_name_edit.text()

        # 设置任务类型（分层结构）
        # 从task_type_edit获取当前显示的任务类型文本
        task_type_text = ""
        if hasattr(self, "task_type_edit") and self.task_type_edit.text():
            task_type_text = self.task_type_edit.text()
            # 尝试从文本中解析父子类型
            if " > " in task_type_text:
                parts = task_type_text.split(" > ", 1)
                if len(parts) == 2:
                    self.current_task_type["parent"] = parts[0]
                    self.current_task_type["child"] = parts[1]
            else:
                # 如果没有分隔符，可能只有父类型
                self.current_task_type["parent"] = task_type_text
                self.current_task_type["child"] = ""

        # 使用current_task_type中的值设置配置
        if self.current_task_type["parent"] and self.current_task_type["child"]:
            task_config["parent_type"] = self.current_task_type["parent"]
            task_config["child_type"] = self.current_task_type["child"]
            # 为了向后兼容，也保留其他格式
            task_config["type"] = self.current_task_type["parent"]
            task_config["subtype"] = self.current_task_type["child"]
            # 确保task_type是字典格式，而不是字符串
            task_config["task_type"] = {
                "parent": self.current_task_type["parent"],
                "child": self.current_task_type["child"],
            }

        task_config["category"] = (
            self.task_category_combo.currentText()
            if self.task_category_combo.currentText() != "无"
            else ""
        )

        # 如果没有id，生成一个新id
        if "id" not in task_config:
            task_config["id"] = str(uuid.uuid4())

        # 更新创建/修改时间
        task_config["updated_at"] = dt.now().isoformat()
        if "created_at" not in task_config:
            task_config["created_at"] = dt.now().isoformat()

        # 启用状态
        task_config["enabled"] = (
            self.enable_schedule_check.isChecked()
            if hasattr(self, "enable_schedule_check")
            else True
        )

        # 计划设置
        schedule = {}
        # 根据当前选中的计划类型设置schedule值
        # 这里应该根据实际UI交互逻辑来设置，暂时使用简单设置
        schedule["type"] = "manual"  # 默认为手动
        task_config["schedule"] = schedule

        # 热键设置
        if hasattr(self, "enable_hotkey_check") and self.enable_hotkey_check.isChecked():
            hotkey = {}
            if hasattr(self, "use_keyboard_radio") and self.use_keyboard_radio.isChecked():
                # 收集键盘热键配置
                modifiers = []
                if hasattr(self, "ctrl_check") and self.ctrl_check.isChecked():
                    modifiers.append("Ctrl")
                if hasattr(self, "alt_check") and self.alt_check.isChecked():
                    modifiers.append("Alt")
                if hasattr(self, "shift_check") and self.shift_check.isChecked():
                    modifiers.append("Shift")
                if hasattr(self, "win_check") and self.win_check.isChecked():
                    modifiers.append("Win")

                key = self.key_combo.currentText() if hasattr(self, "key_combo") else ""

                if modifiers and key:
                    hotkey["type"] = "keyboard"
                    hotkey["modifiers"] = modifiers
                    hotkey["key"] = key
                    hotkey_str = "+".join(modifiers + [key])
                    task_config["hotkey"] = hotkey_str
                    task_config["hotkey_config"] = hotkey
            else:  # 鼠标热键
                # 收集鼠标热键配置
                mouse_buttons = []
                if hasattr(self, "left_button_check") and self.left_button_check.isChecked():
                    mouse_buttons.append("左键")
                if hasattr(self, "middle_button_check") and self.middle_button_check.isChecked():
                    mouse_buttons.append("中键")
                if hasattr(self, "right_button_check") and self.right_button_check.isChecked():
                    mouse_buttons.append("右键")

                use_wheel = hasattr(self, "wheel_check") and self.wheel_check.isChecked()
                wheel_direction = (
                    self.wheel_direction.currentText()
                    if use_wheel and hasattr(self, "wheel_direction")
                    else ""
                )

                if mouse_buttons or use_wheel:
                    hotkey["type"] = "mouse"
                    hotkey["buttons"] = mouse_buttons
                    if use_wheel:
                        hotkey["wheel"] = wheel_direction

                    # 构造热键显示字符串
                    parts = mouse_buttons.copy()
                    if use_wheel:
                        parts.append(wheel_direction)
                    hotkey_str = "+".join(parts)

                    task_config["hotkey"] = hotkey_str
                    task_config["hotkey_config"] = hotkey
        else:
            # 热键未启用
            task_config["hotkey"] = "-"
            task_config.pop("hotkey_config", None)

        # 获取内容
        content_text = ""
        if hasattr(self, "content_edit"):
            content_text = self.content_edit.toPlainText()

        # 设置提醒内容
        task_config["alert_message"] = content_text

        # 设置TTS配置
        tts_config = {}
        if (
            hasattr(self, "tts_radio")
            and hasattr(self, "voice_combo")
            and hasattr(self, "tts_rate_slider")
        ):
            tts_enabled = self.tts_radio.isChecked()
            voice_id = (
                self.voice_combo.currentData() if self.voice_combo.currentIndex() >= 0 else None
            )
            rate = self.tts_rate_slider.value()

            tts_config = {
                "enabled": tts_enabled,
                "text": content_text,
                "voice_id": voice_id,
                "rate": rate,
            }

            # 同时设置旧格式的TTS字段，以保持兼容性
            task_config["enable_tts"] = tts_enabled
            task_config["tts_text"] = content_text
            task_config["tts_voice_id"] = voice_id
            task_config["tts_rate"] = rate

        task_config["tts"] = tts_config

        # 保存声音设置
        if hasattr(self, "play_sound_radio") and hasattr(self, "sound_combo"):
            sound_enabled = self.play_sound_radio.isChecked()
            sound_file = self.sound_combo.currentData()  # 获取关联的文件名
            sound_display = self.sound_combo.currentText()

            # 保存声音配置
            task_config["sound_enabled"] = sound_enabled
            task_config["sound_file"] = sound_file
            task_config["sound_display"] = sound_display

            # 保存播放时间设置
            if hasattr(self, "repeat_spin"):
                task_config["sound_play_time"] = self.repeat_spin.value()

            logger.debug(
                f"保存声音设置: 启用={sound_enabled}, "
                + f"文件={sound_file}, "
                + f"显示名={sound_display}, "
                + f"播放时间={task_config.get('sound_play_time', 10)}"
            )

        # 保存背景色和字体颜色
        if hasattr(self, "bg_color") and hasattr(self, "font_color"):
            # 将QColor对象转换为十六进制色值字符串
            bg_color_str = (
                self.bg_color.name() if self.bg_color and self.bg_color.isValid() else "#8B4513"
            )  # 默认棕色
            font_color_str = (
                self.font_color.name()
                if self.font_color and self.font_color.isValid()
                else "#FF0000"
            )  # 默认红色

            logger.debug(f"保存背景色: {bg_color_str}, 字体颜色: {font_color_str}")

            # 保存到任务配置
            task_config["bg_color"] = bg_color_str
            task_config["font_color"] = font_color_str

            # 保存其他显示设置
            task_config["is_centered"] = (
                self.center_display_check.isChecked()
                if hasattr(self, "center_display_check")
                else True
            )
            task_config["is_top_most"] = (
                self.top_display_check.isChecked() if hasattr(self, "top_display_check") else True
            )

            # 保存透明度
            if hasattr(self, "transparency_slider"):
                transparency = self.transparency_slider.value()
                task_config["transparency"] = transparency

        # 保存自动关闭设置
        if hasattr(self, "auto_close_check"):
            task_config["auto_close"] = self.auto_close_check.isChecked()

            # 保存自动关闭时间
            if hasattr(self, "auto_close_spin"):
                task_config["auto_close_minutes"] = self.auto_close_spin.value()

            if hasattr(self, "auto_close_seconds_spin"):
                task_config["auto_close_seconds"] = self.auto_close_seconds_spin.value()

            logger.debug(
                f"保存自动关闭设置: 启用={task_config['auto_close']}, "
                + f"分钟={task_config.get('auto_close_minutes', 0)}, "
                + f"秒={task_config.get('auto_close_seconds', 0)}"
            )

        # 保存稍后提醒设置
        if hasattr(self, "remind_later_check"):
            task_config["remind_later"] = self.remind_later_check.isChecked()

            # 保存稍后提醒时间
            if hasattr(self, "remind_later_spin"):
                task_config["remind_later_minutes"] = self.remind_later_spin.value()

            if hasattr(self, "remind_later_seconds_spin"):
                task_config["remind_later_seconds"] = self.remind_later_seconds_spin.value()

            # 保存稍后提醒次数
            if hasattr(self, "remind_later_count_spin"):
                task_config["remind_later_count"] = self.remind_later_count_spin.value()

            logger.debug(
                f"保存稍后提醒设置: 启用={task_config.get('remind_later', False)}, "
                + f"分钟={task_config.get('remind_later_minutes', 0)}, "
                + f"秒={task_config.get('remind_later_seconds', 0)}, "
                + f"次数={task_config.get('remind_later_count', 0)}"
            )

        # 动作设置 - 根据任务类型设置动作
        actions = task_config.get("actions", [])
        if not actions:
            # 创建一个默认动作
            action = {
                "id": str(uuid.uuid4()),
                "type": self.current_task_type["child"] or "notification",
                "name": self.current_task_type["child"] or "提醒",
                "params": {"content": content_text or "新任务"},
            }
            actions.append(action)
            logger.debug(f"创建新动作，内容: {content_text}")
        # 更新现有动作的内容
        elif actions and len(actions) > 0:
            if "params" not in actions[0]:
                actions[0]["params"] = {}

            # 确保内容完整保存并且是字符串类型
            actions[0]["params"]["content"] = content_text
            logger.debug(f"更新现有动作内容: {content_text}")

        task_config["actions"] = actions

        return task_config

    def show_task_type_dropdown(self):
        """显示任务类型下拉菜单"""
        # 创建下拉菜单
        menu = QMenu(self)
        menu.setStyleSheet(
            """
            QMenu {
                background-color: white;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                padding: 2px;
            }
            QMenu::item {
                padding: 6px 20px;
                border-radius: 2px;
            }
            QMenu::item:selected {
                background-color: #e8f4fd;
                color: black;
            }
            QMenu::separator {
                height: 1px;
                background-color: #e0e0e0;
                margin: 2px 0px;
            }
        """
        )
        # 为每个任务类型分类创建子菜单
        for parent_type, child_types in self.task_types.items():
            # 创建父级菜单项
            parent_menu = menu.addMenu(parent_type)
            parent_menu.setStyleSheet(menu.styleSheet())  # 应用相同样式
            # 为每个子类型创建菜单项
            for child_type in child_types:
                action = QAction(child_type, self)

                # 修复lambda函数的变量捕获问题
                def make_handler(p_type, c_type):
                    return lambda: self.on_task_type_selected(p_type, c_type)

                action.triggered.connect(make_handler(parent_type, child_type))
                parent_menu.addAction(action)
        # 在文本框下方显示菜单
        if hasattr(self, "task_type_edit"):
            # 计算菜单显示位置（文本框左下角）
            pos = self.task_type_edit.mapToGlobal(self.task_type_edit.rect().bottomLeft())
            menu.exec(pos)


class ClickableLineEdit(QLineEdit):
    """可点击的文本框，点击时执行回调函数"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._click_callback = None
        # 设置为只读，防止用户直接编辑
        self.setReadOnly(True)
        # 设置样式以指示它是可点击的
        self.setStyleSheet(
            """
            QLineEdit {
                background-color: #fcfcfc;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                padding: 2px 4px;
                color: #333333;
            }
            QLineEdit:hover {
                background-color: #f0f0f0;
                border-color: #a0a0a0;
            }
            QLineEdit:focus {
                border-color: #80b0ff;
                background-color: #f5f8ff;
            }
        """
        )

    def set_click_callback(self, callback):
        """设置点击回调函数"""
        self._click_callback = callback

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件处理"""
        super().mousePressEvent(event)
        # 如果有回调函数，则执行
        if self._click_callback:
            self._click_callback()


class NavigableSpinBox(QSpinBox):
    """增强的QSpinBox，支持键盘导航到其他控件"""

    def __init__(
        self,
        parent=None,
        next_focus_widget=None,
        prev_focus_widget=None,
        left_focus_widget=None,
        right_focus_widget=None,
    ):
        super().__init__(parent)
        # 保存导航控件引用
        self.next_widget = next_focus_widget
        self.prev_widget = prev_focus_widget
        self.left_widget = left_focus_widget
        self.right_widget = right_focus_widget

        # 应用垂直按钮样式
        self.apply_vertical_buttons_style()

    def apply_vertical_buttons_style(self):
        """应用系统默认样式 - 保持与QSpinBox一致的立体箭头效果"""
        # 不设置任何自定义样式，使用系统默认的立体箭头样式
        pass

    def set_navigation(
        self, next_widget=None, prev_widget=None, left_widget=None, right_widget=None
    ):
        """设置导航控件"""
        if next_widget:
            self.next_widget = next_widget
        if prev_widget:
            self.prev_widget = prev_widget
        if left_widget:
            self.left_widget = left_widget
        if right_widget:
            self.right_widget = right_widget

    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件，支持方向键导航"""
        key = event.key()

        # 处理上下键（如果已经达到最大/最小值，则导航到下一个/上一个控件）
        if key == Qt.Key_Up:
            if self.value() == self.maximum() and self.prev_widget:
                self.prev_widget.setFocus()
                return
        elif key == Qt.Key_Down:
            if self.value() == self.minimum() and self.next_widget:
                self.next_widget.setFocus()
                return
        # 处理左右键导航
        elif key == Qt.Key_Left and self.left_widget:
            self.left_widget.setFocus()
            return
        elif key == Qt.Key_Right and self.right_widget:
            self.right_widget.setFocus()
            return

        # 如果没有特殊导航处理，则执行默认行为
        super().keyPressEvent(event)
